/**
 * OpenAI Service
 * Centralized OpenAI API management with cost tracking, error handling, and privacy compliance
 */

const OpenAI = require('openai');

class OpenAIService {
    constructor(config, database, logger) {
        this.config = config;
        this.database = database;
        this.logger = logger;
        this.client = null;
        this.isEnabled = false;
        this.usageStats = {
            totalTokens: 0,
            totalCost: 0,
            requestCount: 0,
            lastReset: new Date().toISOString().split('T')[0]
        };
        
        this.initialize();
    }

    async initialize() {
        try {
            if (!this.config.openai?.apiKey) {
                this.logger.warn('OpenAI API key not configured, AI features disabled');
                return;
            }

            this.client = new OpenAI({
                apiKey: this.config.openai.apiKey
            });

            // Load usage stats from database
            await this.loadUsageStats();
            
            this.isEnabled = this.config.ai?.enabled !== false;
            this.logger.info('OpenAI service initialized successfully');
            
        } catch (error) {
            this.logger.error('Failed to initialize OpenAI service:', error);
            this.isEnabled = false;
        }
    }

    async loadUsageStats() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const savedStats = await this.database.getSetting('ai_usage_stats');
            
            if (savedStats) {
                const stats = JSON.parse(savedStats);
                if (stats.lastReset === today) {
                    this.usageStats = stats;
                } else {
                    // Reset daily stats
                    this.usageStats.lastReset = today;
                    await this.saveUsageStats();
                }
            }
        } catch (error) {
            this.logger.error('Error loading usage stats:', error);
        }
    }

    async saveUsageStats() {
        try {
            await this.database.setSetting('ai_usage_stats', JSON.stringify(this.usageStats));
        } catch (error) {
            this.logger.error('Error saving usage stats:', error);
        }
    }

    sanitizeContent(content) {
        // Remove phone numbers, emails, and other sensitive data
        let sanitized = content
            .replace(/\b\d{10,15}\b/g, '[PHONE]')
            .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
            .replace(/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, '[CARD]')
            .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]');
        
        return sanitized;
    }

    calculateCost(model, inputTokens, outputTokens) {
        // Pricing per 1K tokens (as of 2024)
        const pricing = {
            'gpt-4': { input: 0.03, output: 0.06 },
            'gpt-4-turbo': { input: 0.01, output: 0.03 },
            'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
            'gpt-4o-mini': { input: 0.00015, output: 0.0006 }
        };

        const modelPricing = pricing[model] || pricing['gpt-3.5-turbo'];
        const inputCost = (inputTokens / 1000) * modelPricing.input;
        const outputCost = (outputTokens / 1000) * modelPricing.output;
        
        return inputCost + outputCost;
    }

    async checkRateLimits() {
        const limits = this.config.ai?.rateLimits || {};
        const dailyLimit = limits.requestsPerDay || 1000;
        const hourlyCost = limits.maxCostPerHour || 5.0;
        const dailyCost = limits.maxCostPerDay || 20.0;

        // Check daily request limit
        if (this.usageStats.requestCount >= dailyLimit) {
            throw new Error(`Daily request limit exceeded (${dailyLimit})`);
        }

        // Check daily cost limit
        if (this.usageStats.totalCost >= dailyCost) {
            throw new Error(`Daily cost limit exceeded ($${dailyCost})`);
        }

        return true;
    }

    async generateResponse(prompt, options = {}) {
        if (!this.isEnabled || !this.client) {
            throw new Error('OpenAI service not available');
        }

        try {
            await this.checkRateLimits();

            const model = options.model || this.config.ai?.model || 'gpt-3.5-turbo';
            const maxTokens = options.maxTokens || this.config.ai?.maxTokens || 150;
            const temperature = options.temperature || this.config.ai?.temperature || 0.7;

            const sanitizedPrompt = this.sanitizeContent(prompt);

            const response = await this.client.chat.completions.create({
                model: model,
                messages: [{ role: 'user', content: sanitizedPrompt }],
                max_tokens: maxTokens,
                temperature: temperature,
                presence_penalty: 0.1,
                frequency_penalty: 0.1
            });

            const result = response.choices[0].message.content.trim();
            const usage = response.usage;

            // Update usage stats
            this.usageStats.totalTokens += usage.total_tokens;
            this.usageStats.requestCount += 1;
            this.usageStats.totalCost += this.calculateCost(model, usage.prompt_tokens, usage.completion_tokens);
            
            await this.saveUsageStats();

            this.logger.info(`OpenAI API call successful: ${usage.total_tokens} tokens, $${this.calculateCost(model, usage.prompt_tokens, usage.completion_tokens).toFixed(4)}`);

            return {
                content: result,
                usage: usage,
                cost: this.calculateCost(model, usage.prompt_tokens, usage.completion_tokens),
                model: model
            };

        } catch (error) {
            this.logger.error('OpenAI API error:', error);
            throw error;
        }
    }

    async generateContextualResponse(messageData, conversationHistory, userProfile) {
        const language = this.config.ai?.language || 'en';
        const personality = this.config.ai?.personality || 'professional';
        
        let prompt = this.buildResponsePrompt(messageData, conversationHistory, userProfile, language, personality);
        
        try {
            const response = await this.generateResponse(prompt, {
                maxTokens: 100, // Shorter for WhatsApp
                temperature: 0.6
            });

            return {
                content: response.content,
                isAI: true,
                confidence: 0.8,
                cost: response.cost,
                model: response.model
            };
        } catch (error) {
            throw new Error(`AI response generation failed: ${error.message}`);
        }
    }

    buildResponsePrompt(messageData, conversationHistory, userProfile, language, personality) {
        const relationshipContext = userProfile ? `The sender is ${userProfile.name} (${userProfile.type})` : 'Unknown contact';
        const historyContext = conversationHistory.length > 0 ? 
            `Recent conversation:\n${conversationHistory.map(msg => `- ${msg.body}`).join('\n')}` : 
            'No recent conversation history';

        const languageInstructions = language === 'it' ? 
            'Rispondi in italiano in modo naturale e professionale.' :
            'Respond in English naturally and professionally.';

        const personalityInstructions = {
            professional: 'Be professional, concise, and helpful.',
            friendly: 'Be warm, friendly, and approachable.',
            formal: 'Be formal, respectful, and courteous.',
            casual: 'Be casual, relaxed, and conversational.'
        };

        return `You are an AI assistant managing WhatsApp messages for a busy professional.

Context:
- ${relationshipContext}
- Current message: "${messageData.body}"
- ${historyContext}

Instructions:
- ${languageInstructions}
- ${personalityInstructions[personality] || personalityInstructions.professional}
- Keep response under 200 characters for WhatsApp
- Be helpful and acknowledge the message appropriately
- If it's a question, provide a brief helpful response or indicate you'll follow up
- If it's a request, acknowledge it and indicate next steps

Generate an appropriate response:`;
    }

    async analyzeSentiment(text) {
        if (!this.isEnabled) return { sentiment: 'neutral', confidence: 0 };

        try {
            const prompt = `Analyze the sentiment of this message and respond with only one word: positive, negative, or neutral.

Message: "${this.sanitizeContent(text)}"

Sentiment:`;

            const response = await this.generateResponse(prompt, {
                maxTokens: 10,
                temperature: 0.1
            });

            const sentiment = response.content.toLowerCase().trim();
            return {
                sentiment: ['positive', 'negative', 'neutral'].includes(sentiment) ? sentiment : 'neutral',
                confidence: 0.8
            };
        } catch (error) {
            this.logger.error('Sentiment analysis failed:', error);
            return { sentiment: 'neutral', confidence: 0 };
        }
    }

    async generateSummary(messages, summaryType = 'daily') {
        if (!this.isEnabled) throw new Error('AI service not available');

        const language = this.config.ai?.language || 'en';
        const prompt = this.buildSummaryPrompt(messages, summaryType, language);

        try {
            const response = await this.generateResponse(prompt, {
                maxTokens: 500,
                temperature: 0.3
            });

            return {
                content: response.content,
                isAI: true,
                cost: response.cost,
                model: response.model
            };
        } catch (error) {
            throw new Error(`AI summary generation failed: ${error.message}`);
        }
    }

    buildSummaryPrompt(messages, summaryType, language) {
        const messageList = messages.map(msg => 
            `- ${msg.contact_name || 'Unknown'}: ${this.sanitizeContent(msg.body)}`
        ).join('\n');

        const languageInstructions = language === 'it' ? 
            'Scrivi il riassunto in italiano.' :
            'Write the summary in English.';

        return `Create a ${summaryType} summary of these WhatsApp messages.

Messages:
${messageList}

Instructions:
- ${languageInstructions}
- Provide a natural language narrative summary
- Identify key topics, urgent items, and action items
- Highlight any deadlines, meetings, or file requests
- Note the overall sentiment and any concerning conversations
- Suggest recommended actions if appropriate
- Keep it concise but informative

Summary:`;
    }

    async getUsageStats() {
        return {
            ...this.usageStats,
            isEnabled: this.isEnabled,
            dailyLimitRemaining: (this.config.ai?.rateLimits?.requestsPerDay || 1000) - this.usageStats.requestCount,
            costLimitRemaining: (this.config.ai?.rateLimits?.maxCostPerDay || 20.0) - this.usageStats.totalCost
        };
    }

    async toggleService(enabled) {
        this.isEnabled = enabled;
        this.config.ai = { ...this.config.ai, enabled };
        this.config.saveUserConfig();
        this.logger.info(`OpenAI service ${enabled ? 'enabled' : 'disabled'}`);
        return this.isEnabled;
    }
}

module.exports = OpenAIService;
