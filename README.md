# WhatsApp Intelligent Bot

An intelligent WhatsApp bot that automatically manages your messages with smart responses, message analysis, and Telegram notifications.

## Features

### Core Features
- **Automatic Response System**: Responds to common message patterns and contact-specific rules
- **Daily Summary Generation**: Creates bullet-point summaries at 8:00 PM daily
- **Important Message Detection**: Filters messages that need your attention
- **Telegram Notifications**: Sends alerts and summaries via Telegram

### Advanced Features
- **User Profiles**: Personalized responses based on contact types
- **Business Hours**: Different responses during/outside business hours
- **Do Not Disturb Mode**: Auto-responds and forwards only critical messages
- **Message Analysis**: Detects urgency, requests, questions, and message types
- **Progressive Learning**: Adapts to your response patterns over time

## Prerequisites

- Node.js (v16 or higher)
- A Telegram bot token (get from @BotFather)
- Your Telegram chat ID (get from @userinfobot)

## Installation

1. **Clone and setup the project:**
   ```bash
   git clone <your-repo-url>
   cd whatsappResponder
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your credentials:
   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   TELEGRAM_CHAT_ID=your_telegram_chat_id_here
   ```

3. **Start the bot:**
   ```bash
   npm run dev
   ```

## Setup Instructions

### 1. Create a Telegram Bot

1. Message @BotFather on Telegram
2. Send `/newbot` and follow the instructions
3. Copy the bot token to your `.env` file

### 2. Get Your Telegram Chat ID

1. Message @userinfobot on Telegram
2. Copy your chat ID to your `.env` file

### 3. First Run

1. Run `npm run dev`
2. Scan the QR code with WhatsApp on your phone
3. The bot will send a confirmation message to Telegram

## Configuration

### User Profiles

Edit `config/user-config.json` to customize:

```json
{
  "userProfiles": {
    "<EMAIL>": {
      "name": "Marco",
      "type": "colleague",
      "autoRespond": true,
      "customResponse": "I'm in a meeting. I'll get back to you soon.",
      "priority": "normal"
    }
  },
  "importantContacts": ["<EMAIL>"],
  "urgencyKeywords": ["urgent", "emergency", "asap", "problem"]
}
```

### Response Patterns

The bot recognizes these message types:
- **Greetings**: "Hi", "Hello", "Good morning"
- **File Requests**: "Can you send me the file?"
- **Meeting Questions**: "When is our meeting?"
- **Availability**: "Are you free?"

### Business Hours

Configure in `config/user-config.json`:
```json
{
  "businessHours": {
    "enabled": true,
    "schedule": {
      "monday": { "start": "09:00", "end": "18:00" },
      "friday": { "start": "09:00", "end": "18:00" }
    },
    "outOfHoursResponse": "I'm outside business hours. I'll respond tomorrow."
  }
}
```

## Usage

### Telegram Commands

- `/status` - Check bot status and statistics
- `/summary` - Generate current summary
- `/contacts` - List important contacts
- `/settings` - View current settings
- `/dnd` - Toggle Do Not Disturb mode
- `/help` - Show help message

### Automatic Features

1. **Auto Responses**: The bot automatically responds to common patterns
2. **Attention Alerts**: You'll receive Telegram notifications for important messages
3. **Daily Summaries**: At 8 PM, you'll get a summary of the day's messages
4. **Urgent Detection**: Messages with urgent keywords trigger immediate alerts

## File Structure

```
whatsappResponder/
├── src/
│   ├── index.js              # Main entry point
│   ├── config/
│   │   └── config.js         # Configuration management
│   ├── database/
│   │   └── database.js       # SQLite database operations
│   ├── telegram/
│   │   └── telegramBot.js    # Telegram integration
│   ├── processors/
│   │   └── messageProcessor.js # Message analysis
│   ├── responses/
│   │   └── responseSystem.js  # Auto-response logic
│   └── summary/
│       └── summaryGenerator.js # Daily summaries
├── config/
│   └── user-config.json      # User customization
├── data/                     # Database files
├── logs/                     # Log files
├── sessions/                 # WhatsApp session data
└── .env                      # Environment variables
```

## Troubleshooting

### Common Issues

1. **QR Code not appearing**: Check console output for errors
2. **Telegram not working**: Verify bot token and chat ID
3. **Database errors**: Ensure `data/` directory exists and is writable
4. **WhatsApp disconnects**: This is normal; the bot will reconnect automatically

### Logs

Check the log files in the `logs/` directory:
- `error.log` - Error messages
- `combined.log` - All log messages

### Reset WhatsApp Session

If you need to re-authenticate WhatsApp:
```bash
rm -rf sessions/
npm run dev
```

## Development

### Running in Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Adding Custom Response Patterns

Edit `src/config/config.js` to add new patterns:
```javascript
customPattern: {
    patterns: [/your regex pattern/i],
    responses: ["Your response here"]
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs
3. Create an issue on GitHub

---

**Note**: This bot is for personal use. Ensure compliance with WhatsApp's Terms of Service.
