# WhatsApp Intelligent Bot

An intelligent WhatsApp bot that automatically manages your messages with smart responses, message analysis, and Telegram notifications.

## Features

### 🤖 AI-Powered Features (NEW!)
- **AI-Generated Responses**: Context-aware responses using OpenAI GPT models
- **Intelligent Daily Summaries**: Natural language summaries with insights and action items
- **Sentiment Analysis**: Analyzes conversation sentiment and identifies concerning messages
- **Smart Notifications**: AI-powered explanations and suggested response options
- **Multi-language Support**: Natural AI responses in English and Italian
- **Cost Management**: Built-in usage monitoring and budget controls

### Core Features
- **Automatic Response System**: AI-powered or pattern-based responses with intelligent fallback
- **Daily Summary Generation**: AI-enhanced narrative summaries or traditional bullet-point format
- **Important Message Detection**: Advanced AI analysis plus keyword-based filtering
- **Telegram Notifications**: Interactive alerts with AI-generated context and suggestions

### Advanced Features
- **User Profiles**: AI-aware personalized responses based on contact relationships
- **Business Hours**: Time-based response behavior with AI personality adaptation
- **Do Not Disturb Mode**: Smart quiet hours with AI-powered exception handling
- **Message Analysis**: AI-enhanced detection of urgency, requests, questions, and sentiment
- **Hybrid Intelligence**: Seamless fallback between AI and pattern-based systems

## Prerequisites

- Node.js (v16 or higher)
- A Telegram bot token (get from @BotFather)
- Your Telegram chat ID (get from @userinfobot)

## Installation

1. **Clone and setup the project:**
   ```bash
   git clone <your-repo-url>
   cd whatsappResponder
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your credentials:
   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   TELEGRAM_CHAT_ID=your_telegram_chat_id_here

   # Optional: Add OpenAI API key for AI features
   OPENAI_API_KEY=your_openai_api_key_here
   AI_ENABLED=true
   ```

3. **Start the bot:**
   ```bash
   npm run dev
   ```

## Setup Instructions

### 1. Create a Telegram Bot

1. Message @BotFather on Telegram
2. Send `/newbot` and follow the instructions
3. Copy the bot token to your `.env` file

### 2. Get Your Telegram Chat ID

1. Message @userinfobot on Telegram
2. Copy your chat ID to your `.env` file

### 3. First Run

1. Run `npm run dev`
2. Scan the QR code with WhatsApp on your phone
3. The bot will send a confirmation message to Telegram

## Configuration

### User Profiles

Edit `config/user-config.json` to customize:

```json
{
  "userProfiles": {
    "<EMAIL>": {
      "name": "Marco",
      "type": "colleague",
      "autoRespond": true,
      "customResponse": "I'm in a meeting. I'll get back to you soon.",
      "priority": "normal"
    }
  },
  "importantContacts": ["<EMAIL>"],
  "urgencyKeywords": ["urgent", "emergency", "asap", "problem"]
}
```

### Response Patterns

The bot recognizes these message types:
- **Greetings**: "Hi", "Hello", "Good morning"
- **File Requests**: "Can you send me the file?"
- **Meeting Questions**: "When is our meeting?"
- **Availability**: "Are you free?"

### Business Hours

Configure in `config/user-config.json`:
```json
{
  "businessHours": {
    "enabled": true,
    "schedule": {
      "monday": { "start": "09:00", "end": "18:00" },
      "friday": { "start": "09:00", "end": "18:00" }
    },
    "outOfHoursResponse": "I'm outside business hours. I'll respond tomorrow."
  }
}
```

## 🤖 AI Features

### Quick Start with AI
1. **Get OpenAI API Key**: Sign up at [OpenAI](https://platform.openai.com/api-keys)
2. **Add to Environment**: Add `OPENAI_API_KEY=your_key` to `.env`
3. **Check Status**: Run `npm run ai status`
4. **Test AI**: Run `npm run ai test`

### AI Management Commands
```bash
# Check AI status and configuration
npm run ai status

# Toggle AI features on/off
npm run ai toggle

# View usage and costs
npm run ai costs

# Configure AI settings
npm run ai config model gpt-3.5-turbo
npm run ai config language it
npm run ai config personality friendly

# Test AI functionality
npm run ai test
```

### Cost Management
- **Built-in Budget Controls**: Set daily/hourly spending limits
- **Real-time Monitoring**: Track usage and costs in real-time
- **Model Selection**: Choose from gpt-4, gpt-3.5-turbo, or gpt-4o-mini
- **Smart Fallbacks**: Automatically falls back to pattern-based responses

### Privacy & Security
- **Data Sanitization**: Removes sensitive information before API calls
- **Local Storage**: All data remains on your device
- **No Training**: Your data is not used to train AI models
- **User Control**: Complete control over AI feature usage

## Usage

### Telegram Commands

#### Basic Commands
- `/status` - Check bot status and statistics
- `/summary` - Generate current summary (AI-enhanced if enabled)
- `/contacts` - List important contacts
- `/dnd` - Toggle Do Not Disturb mode
- `/help` - Show help message

#### AI Commands (NEW!)
- `/ai status` - Check AI service status and usage
- `/ai toggle` - Enable/disable AI features
- `/ai costs` - View detailed AI usage and costs

### Automatic Features

1. **AI-Powered Auto Responses**: Context-aware responses using OpenAI GPT (with pattern-based fallback)
2. **Intelligent Attention Alerts**: AI-enhanced notifications with context explanations and suggested responses
3. **Smart Daily Summaries**: AI-generated narrative summaries with insights, action items, and sentiment analysis
4. **Advanced Urgent Detection**: AI-powered message analysis plus keyword-based detection
5. **Cost-Aware Operation**: Automatic budget management and usage monitoring for AI features

## File Structure

```
whatsappResponder/
├── src/
│   ├── index.js              # Main entry point
│   ├── config/
│   │   └── config.js         # Configuration management
│   ├── database/
│   │   └── database.js       # SQLite database operations
│   ├── telegram/
│   │   └── telegramBot.js    # Telegram integration
│   ├── processors/
│   │   └── messageProcessor.js # Message analysis
│   ├── responses/
│   │   └── responseSystem.js  # Auto-response logic
│   └── summary/
│       └── summaryGenerator.js # Daily summaries
├── config/
│   └── user-config.json      # User customization
├── data/                     # Database files
├── logs/                     # Log files
├── sessions/                 # WhatsApp session data
└── .env                      # Environment variables
```

## Troubleshooting

### Common Issues

1. **QR Code not appearing**: Check console output for errors
2. **Telegram not working**: Verify bot token and chat ID
3. **Database errors**: Ensure `data/` directory exists and is writable
4. **WhatsApp disconnects**: This is normal; the bot will reconnect automatically

### Logs

Check the log files in the `logs/` directory:
- `error.log` - Error messages
- `combined.log` - All log messages

### Reset WhatsApp Session

If you need to re-authenticate WhatsApp:
```bash
rm -rf sessions/
npm run dev
```

## Development

### Running in Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Adding Custom Response Patterns

Edit `src/config/config.js` to add new patterns:
```javascript
customPattern: {
    patterns: [/your regex pattern/i],
    responses: ["Your response here"]
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs
3. Create an issue on GitHub

---

**Note**: This bot is for personal use. Ensure compliance with WhatsApp's Terms of Service.
