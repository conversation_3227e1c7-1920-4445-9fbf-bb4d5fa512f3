#!/usr/bin/env node

/**
 * AI Manager Script
 * Manage AI features, costs, and configuration
 */

require('dotenv').config();
const Database = require('../src/database/database');
const Config = require('../src/config/config');
const OpenAIService = require('../src/ai/openaiService');

const logger = {
    info: console.log,
    error: console.error,
    warn: console.warn
};

async function showAIStatus() {
    let database = null;
    try {
        const config = new Config();
        database = new Database();
        await database.initialize();

        // Check AI enabled status from database
        const aiEnabledSetting = await database.getSetting('ai_enabled');
        const isAIEnabled = aiEnabledSetting !== 'false';

        console.log('🤖 AI Service Status');
        console.log('===================');
        console.log(`Status: ${isAIEnabled ? '🟢 Enabled' : '🔴 Disabled'}`);
        console.log(`API Key: ${config.openai?.apiKey ? '✅ Configured' : '❌ Missing'}`);
        console.log('');

        // Get usage stats if AI is enabled
        if (isAIEnabled && config.openai?.apiKey) {
            try {
                const aiService = new OpenAIService(config, database, logger);
                const stats = await aiService.getUsageStats();

                console.log('📊 Today\'s Usage:');
                console.log(`• Requests: ${stats.requestCount}`);
                console.log(`• Total tokens: ${stats.totalTokens}`);
                console.log(`• Cost: $${stats.totalCost.toFixed(4)}`);
                console.log('');

                console.log('📈 Limits:');
                console.log(`• Daily requests remaining: ${stats.dailyLimitRemaining}`);
                console.log(`• Daily cost remaining: $${stats.costLimitRemaining.toFixed(2)}`);
                console.log('');
            } catch (error) {
                console.log('📊 Today\'s Usage:');
                console.log('• Requests: 0');
                console.log('• Total tokens: 0');
                console.log('• Cost: $0.0000');
                console.log('');

                console.log('📈 Limits:');
                console.log('• Daily requests remaining: 1000');
                console.log('• Daily cost remaining: $20.00');
                console.log('');
            }
        } else {
            console.log('📊 Today\'s Usage:');
            console.log('• Requests: 0 (AI disabled)');
            console.log('• Total tokens: 0');
            console.log('• Cost: $0.0000');
            console.log('');

            console.log('📈 Limits:');
            console.log('• Daily requests remaining: 1000');
            console.log('• Daily cost remaining: $20.00');
            console.log('');
        }

        console.log('⚙️ Features:');

        // Load user config to check feature settings
        let userConfig = {};
        try {
            const fs = require('fs');
            const userConfigPath = './config/user-config.json';
            if (fs.existsSync(userConfigPath)) {
                userConfig = JSON.parse(fs.readFileSync(userConfigPath, 'utf8'));
            }
        } catch (error) {
            console.log('Warning: Could not load user config');
        }

        const aiFeatures = userConfig.ai?.features || {};
        console.log(`• Smart responses: ${isAIEnabled && aiFeatures.smartResponses ? '✅' : '❌'}`);
        console.log(`• Intelligent summaries: ${isAIEnabled && aiFeatures.intelligentSummaries ? '✅' : '❌'}`);
        console.log(`• Sentiment analysis: ${isAIEnabled && aiFeatures.sentimentAnalysis ? '✅' : '❌'}`);
        console.log(`• Contextual notifications: ${isAIEnabled && aiFeatures.contextualNotifications ? '✅' : '❌'}`);
        console.log('');

        console.log('🌐 Settings:');
        console.log(`• Model: ${userConfig.ai?.model || config.ai?.model || config.openai?.model || 'gpt-3.5-turbo'}`);
        console.log(`• Language: ${userConfig.ai?.language || config.ai?.language || 'en'}`);
        console.log(`• Personality: ${userConfig.ai?.personality || config.ai?.personality || 'professional'}`);
        console.log(`• Max tokens: ${userConfig.ai?.maxTokens || config.ai?.maxTokens || 150}`);
        console.log(`• Temperature: ${userConfig.ai?.temperature || config.ai?.temperature || 0.7}`);

    } catch (error) {
        console.error('❌ Error getting AI status:', error.message);
    } finally {
        if (database) {
            await database.close();
        }
    }
}

async function toggleAI() {
    let database = null;
    try {
        const config = new Config();
        database = new Database();
        await database.initialize();

        // Get current status
        const currentStatus = await database.getSetting('ai_enabled');
        const isCurrentlyEnabled = currentStatus !== 'false';

        // Toggle status
        const newStatus = !isCurrentlyEnabled;
        await database.setSetting('ai_enabled', newStatus.toString());

        console.log(`🤖 AI service ${newStatus ? 'enabled' : 'disabled'}`);

    } catch (error) {
        console.error('❌ Error toggling AI service:', error.message);
    } finally {
        if (database) {
            await database.close();
        }
    }
}

async function showCosts() {
    try {
        const database = new Database();
        await database.initialize();
        
        // Get AI usage statistics from database
        const usageStats = await database.all(`
            SELECT 
                date,
                SUM(requests_count) as total_requests,
                SUM(total_tokens) as total_tokens,
                SUM(total_cost) as total_cost,
                model_used
            FROM ai_usage_stats 
            WHERE date >= date('now', '-7 days')
            GROUP BY date
            ORDER BY date DESC
        `);
        
        console.log('💰 AI Usage & Costs (Last 7 Days)');
        console.log('==================================');
        
        if (usageStats.length === 0) {
            console.log('No usage data available');
            await database.close();
            return;
        }
        
        let totalCost = 0;
        let totalRequests = 0;
        let totalTokens = 0;
        
        usageStats.forEach(stat => {
            console.log(`📅 ${stat.date}:`);
            console.log(`   • Requests: ${stat.total_requests || 0}`);
            console.log(`   • Tokens: ${stat.total_tokens || 0}`);
            console.log(`   • Cost: $${(stat.total_cost || 0).toFixed(4)}`);
            console.log(`   • Model: ${stat.model_used || 'N/A'}`);
            console.log('');
            
            totalCost += stat.total_cost || 0;
            totalRequests += stat.total_requests || 0;
            totalTokens += stat.total_tokens || 0;
        });
        
        console.log('📊 Weekly Summary:');
        console.log(`• Total requests: ${totalRequests}`);
        console.log(`• Total tokens: ${totalTokens}`);
        console.log(`• Total cost: $${totalCost.toFixed(4)}`);
        console.log(`• Average cost per request: $${totalRequests > 0 ? (totalCost / totalRequests).toFixed(4) : '0.0000'}`);
        
        await database.close();
    } catch (error) {
        console.error('❌ Error getting cost information:', error.message);
    }
}

async function configureAI(feature, value) {
    try {
        const config = new Config();
        
        if (!config.ai) {
            config.ai = {};
        }
        
        switch (feature) {
            case 'model':
                const validModels = ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o-mini'];
                if (!validModels.includes(value)) {
                    console.error(`❌ Invalid model. Valid options: ${validModels.join(', ')}`);
                    return;
                }
                config.ai.model = value;
                break;
                
            case 'language':
                if (!['en', 'it'].includes(value)) {
                    console.error('❌ Invalid language. Valid options: en, it');
                    return;
                }
                config.ai.language = value;
                break;
                
            case 'personality':
                const validPersonalities = ['professional', 'friendly', 'formal', 'casual'];
                if (!validPersonalities.includes(value)) {
                    console.error(`❌ Invalid personality. Valid options: ${validPersonalities.join(', ')}`);
                    return;
                }
                config.ai.personality = value;
                break;
                
            case 'temperature':
                const temp = parseFloat(value);
                if (isNaN(temp) || temp < 0 || temp > 2) {
                    console.error('❌ Invalid temperature. Must be between 0 and 2');
                    return;
                }
                config.ai.temperature = temp;
                break;
                
            case 'maxTokens':
                const tokens = parseInt(value);
                if (isNaN(tokens) || tokens < 10 || tokens > 1000) {
                    console.error('❌ Invalid max tokens. Must be between 10 and 1000');
                    return;
                }
                config.ai.maxTokens = tokens;
                break;
                
            default:
                console.error('❌ Invalid feature. Valid options: model, language, personality, temperature, maxTokens');
                return;
        }
        
        config.saveUserConfig();
        console.log(`✅ AI ${feature} set to: ${value}`);
        
    } catch (error) {
        console.error('❌ Error configuring AI:', error.message);
    }
}

async function testAI() {
    let database = null;
    try {
        const config = new Config();
        database = new Database();
        await database.initialize();

        // Check if AI is enabled
        const aiEnabledSetting = await database.getSetting('ai_enabled');
        const isAIEnabled = aiEnabledSetting !== 'false';

        if (!isAIEnabled) {
            console.log('❌ AI service is not enabled');
            console.log('Run "npm run ai toggle" to enable it first');
            return;
        }

        if (!config.openai?.apiKey) {
            console.log('❌ OpenAI API key not configured');
            console.log('Add OPENAI_API_KEY to your .env file');
            return;
        }

        console.log('🧪 Testing AI service...');

        const aiService = new OpenAIService(config, database, logger);
        await aiService.initialize();

        if (!aiService.isEnabled) {
            console.log('❌ AI service failed to initialize properly');
            return;
        }

        const testPrompt = 'Respond with "AI test successful" if you can understand this message.';
        const response = await aiService.generateResponse(testPrompt, {
            maxTokens: 20,
            temperature: 0.1
        });

        console.log('✅ AI Test Results:');
        console.log(`• Response: ${response.content}`);
        console.log(`• Model: ${response.model}`);
        console.log(`• Cost: $${response.cost.toFixed(4)}`);
        console.log(`• Tokens: ${response.usage.total_tokens}`);

    } catch (error) {
        console.error('❌ AI test failed:', error.message);
        if (error.message.includes('401')) {
            console.log('💡 This usually means your API key is invalid or expired');
        } else if (error.message.includes('429')) {
            console.log('💡 Rate limit exceeded. Try again in a few minutes');
        } else if (error.message.includes('insufficient_quota')) {
            console.log('💡 Your OpenAI account has insufficient credits');
        }
    } finally {
        if (database) {
            await database.close();
        }
    }
}

// Main execution
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];

switch (command) {
    case 'status':
        showAIStatus();
        break;
    case 'toggle':
        toggleAI();
        break;
    case 'costs':
        showCosts();
        break;
    case 'config':
        if (!arg1 || !arg2) {
            console.log('Usage: npm run ai config <feature> <value>');
            console.log('Features: model, language, personality, temperature, maxTokens');
        } else {
            configureAI(arg1, arg2);
        }
        break;
    case 'test':
        testAI();
        break;
    default:
        console.log('🤖 AI Manager');
        console.log('=============');
        console.log('');
        console.log('Usage:');
        console.log('  npm run ai <command> [options]');
        console.log('');
        console.log('Commands:');
        console.log('  status              - Show AI service status');
        console.log('  toggle              - Enable/disable AI service');
        console.log('  costs               - Show usage and costs');
        console.log('  config <key> <val>  - Configure AI settings');
        console.log('  test                - Test AI service');
        console.log('');
        console.log('Examples:');
        console.log('  npm run ai status');
        console.log('  npm run ai config model gpt-4');
        console.log('  npm run ai config language it');
        console.log('  npm run ai config personality friendly');
        break;
}
