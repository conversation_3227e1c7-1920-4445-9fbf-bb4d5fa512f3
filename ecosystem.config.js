module.exports = {
  apps: [{
    name: 'whatsapp-bot',
    script: 'src/index.js',
    
    // Instance configuration
    instances: 1,
    exec_mode: 'fork',
    
    // Auto restart configuration
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Environment
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_development: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    
    // Logging
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Advanced options
    node_args: '--max-old-space-size=1024',
    kill_timeout: 5000,
    listen_timeout: 3000,
    
    // Monitoring
    pmx: true,
    
    // Source map support
    source_map_support: true,
    
    // Merge logs
    merge_logs: true,
    
    // Cron restart (optional - restart daily at 3 AM)
    cron_restart: '0 3 * * *',
    
    // Health check
    health_check_grace_period: 3000
  }],

  // Deployment configuration
  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:username/whatsapp-bot.git',
      path: '/var/www/whatsapp-bot',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run test && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    },
    staging: {
      user: 'node',
      host: 'staging-server.com',
      ref: 'origin/develop',
      repo: '**************:username/whatsapp-bot.git',
      path: '/var/www/whatsapp-bot-staging',
      'post-deploy': 'npm install && npm run test && pm2 reload ecosystem.config.js --env staging'
    }
  }
};
