# WhatsApp Intelligent Bot - Deployment Guide

## Local Development Deployment

### Prerequisites
- Node.js 16+ installed
- Git installed
- WhatsApp account
- Telegram account

### Setup Steps
1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd whatsappResponder
   npm install
   ```

2. **Configure Environment**
   ```bash
   npm run setup
   # Follow the interactive setup wizard
   ```

3. **Test Installation**
   ```bash
   npm test
   ```

4. **Start Development**
   ```bash
   npm run dev
   ```

## Production Deployment

### Option 1: VPS/Cloud Server (Recommended)

#### Server Requirements
- **OS**: Ubuntu 20.04+ or similar Linux distribution
- **RAM**: Minimum 1GB, recommended 2GB+
- **Storage**: 10GB+ available space
- **Network**: Stable internet connection

#### Installation Steps

1. **Server Setup**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2 for process management
   sudo npm install -g pm2
   
   # Install Git
   sudo apt install git -y
   ```

2. **Deploy Application**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd whatsappResponder
   
   # Install dependencies
   npm install --production
   
   # Setup configuration
   cp .env.example .env
   # Edit .env with your credentials
   nano .env
   ```

3. **Configure PM2**
   ```bash
   # Create PM2 ecosystem file
   cat > ecosystem.config.js << EOF
   module.exports = {
     apps: [{
       name: 'whatsapp-bot',
       script: 'src/index.js',
       instances: 1,
       autorestart: true,
       watch: false,
       max_memory_restart: '1G',
       env: {
         NODE_ENV: 'production'
       },
       error_file: './logs/pm2-error.log',
       out_file: './logs/pm2-out.log',
       log_file: './logs/pm2-combined.log',
       time: true
     }]
   };
   EOF
   ```

4. **Start with PM2**
   ```bash
   # Create necessary directories
   mkdir -p logs data config sessions
   
   # Start the application
   pm2 start ecosystem.config.js
   
   # Save PM2 configuration
   pm2 save
   
   # Setup PM2 to start on boot
   pm2 startup
   # Follow the instructions provided by PM2
   ```

5. **Initial Authentication**
   ```bash
   # Monitor logs to see QR code
   pm2 logs whatsapp-bot
   
   # Scan QR code with WhatsApp
   # Check Telegram for confirmation
   ```

### Option 2: Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs data config sessions

# Expose port (if needed for health checks)
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  whatsapp-bot:
    build: .
    container_name: whatsapp-intelligent-bot
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
      - ./sessions:/app/sessions
    healthcheck:
      test: ["CMD", "node", "-e", "process.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
```

#### Deploy with Docker
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

### Option 3: Cloud Platform Deployment

#### Heroku
```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-whatsapp-bot

# Set environment variables
heroku config:set TELEGRAM_BOT_TOKEN=your_token
heroku config:set TELEGRAM_CHAT_ID=your_chat_id

# Deploy
git push heroku main

# Scale
heroku ps:scale worker=1
```

#### Railway
1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on push

#### DigitalOcean App Platform
1. Create new app from GitHub repository
2. Configure environment variables
3. Deploy with automatic scaling

## Security Considerations

### Environment Variables
```bash
# Never commit these to version control
TELEGRAM_BOT_TOKEN=your_secret_token
TELEGRAM_CHAT_ID=your_chat_id
OPENAI_API_KEY=your_openai_key  # if using AI features
```

### File Permissions
```bash
# Secure configuration files
chmod 600 .env
chmod 700 data/
chmod 700 sessions/
```

### Firewall Configuration
```bash
# Basic UFW setup (Ubuntu)
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
# Only allow necessary ports
```

### SSL/TLS (if exposing web interface)
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

## Monitoring and Maintenance

### Health Checks
```bash
# Create health check script
cat > health-check.sh << EOF
#!/bin/bash
if pm2 list | grep -q "online"; then
    echo "Bot is running"
    exit 0
else
    echo "Bot is not running"
    pm2 restart whatsapp-bot
    exit 1
fi
EOF

chmod +x health-check.sh

# Add to crontab for regular checks
crontab -e
# Add: */5 * * * * /path/to/health-check.sh
```

### Log Rotation
```bash
# Configure logrotate
sudo cat > /etc/logrotate.d/whatsapp-bot << EOF
/path/to/whatsappResponder/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

### Backup Strategy
```bash
# Create backup script
cat > backup.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/whatsapp-bot"
mkdir -p $BACKUP_DIR

# Backup database
cp data/whatsapp_bot.db $BACKUP_DIR/db_$DATE.db

# Backup configuration
cp -r config/ $BACKUP_DIR/config_$DATE/

# Backup sessions (optional, contains auth data)
cp -r sessions/ $BACKUP_DIR/sessions_$DATE/

# Clean old backups (keep 7 days)
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "config_*" -mtime +7 -exec rm -rf {} +
find $BACKUP_DIR -name "sessions_*" -mtime +7 -exec rm -rf {} +
EOF

chmod +x backup.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup.sh
```

### Updates and Maintenance
```bash
# Create update script
cat > update.sh << EOF
#!/bin/bash
cd /path/to/whatsappResponder

# Backup before update
./backup.sh

# Pull latest changes
git pull origin main

# Install dependencies
npm install --production

# Test the update
npm test

# Restart with PM2
pm2 restart whatsapp-bot

echo "Update completed"
EOF

chmod +x update.sh
```

## Performance Optimization

### Memory Management
```javascript
// In ecosystem.config.js
max_memory_restart: '1G',
node_args: '--max-old-space-size=1024'
```

### Database Optimization
```bash
# Regular database maintenance
sqlite3 data/whatsapp_bot.db "VACUUM;"
sqlite3 data/whatsapp_bot.db "ANALYZE;"
```

### Process Monitoring
```bash
# Monitor with PM2
pm2 monit

# System monitoring
htop
iostat -x 1
```

## Troubleshooting Production Issues

### Common Problems

1. **Bot Stops Responding**
   ```bash
   pm2 restart whatsapp-bot
   pm2 logs whatsapp-bot --lines 100
   ```

2. **WhatsApp Session Expired**
   ```bash
   pm2 stop whatsapp-bot
   rm -rf sessions/
   pm2 start whatsapp-bot
   # Scan new QR code
   ```

3. **Database Corruption**
   ```bash
   pm2 stop whatsapp-bot
   cp data/whatsapp_bot.db data/whatsapp_bot.db.backup
   sqlite3 data/whatsapp_bot.db ".recover" | sqlite3 data/whatsapp_bot_recovered.db
   mv data/whatsapp_bot_recovered.db data/whatsapp_bot.db
   pm2 start whatsapp-bot
   ```

4. **High Memory Usage**
   ```bash
   pm2 restart whatsapp-bot
   # Consider increasing max_memory_restart limit
   ```

### Emergency Recovery
```bash
# Complete reset (last resort)
pm2 stop whatsapp-bot
rm -rf sessions/ data/
mkdir -p data sessions
pm2 start whatsapp-bot
# Reconfigure from scratch
```

## Scaling Considerations

For high-volume usage:
- Use Redis for session storage
- Implement message queuing
- Consider horizontal scaling
- Monitor database performance
- Implement rate limiting

Remember to test all deployment procedures in a staging environment before applying to production!
