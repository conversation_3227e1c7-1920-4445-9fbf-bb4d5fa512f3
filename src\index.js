/**
 * WhatsApp Bot - Main Entry Point
 * Intelligent WhatsApp message management with Telegram notifications
 */

const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const cron = require('node-cron');
const winston = require('winston');
const path = require('path');

// Import our modules
const TelegramBot = require('./telegram/telegramBot');
const Database = require('./database/database');
const MessageProcessor = require('./processors/messageProcessor');
const ResponseSystem = require('./responses/responseSystem');
const SummaryGenerator = require('./summary/summaryGenerator');
const Config = require('./config/config');

// Setup logging
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'whatsapp-bot' },
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
            format: winston.format.simple()
        })
    ]
});

class WhatsAppBot {
    constructor() {
        this.client = null;
        this.telegramBot = null;
        this.database = null;
        this.messageProcessor = null;
        this.responseSystem = null;
        this.summaryGenerator = null;
        this.config = new Config();
        this.isReady = false;
    }

    async initialize() {
        try {
            logger.info('Initializing WhatsApp Bot...');

            // Initialize database
            this.database = new Database();
            await this.database.initialize();

            // Initialize Telegram bot with AI support
            this.telegramBot = new TelegramBot(
                this.config.telegram.botToken,
                this.config.telegram.chatId,
                this.config,
                this.database,
                logger
            );
            await this.telegramBot.initialize();

            // Set up Telegram bot callbacks
            this.telegramBot.setCallbacks({
                onSummaryRequest: () => this.summaryGenerator.generateDailySummary(),
                onToggleDND: () => this.toggleDNDMode(),
                onContactStats: () => this.getContactStats(),
                getSystemStatus: () => this.getSystemStatus(),
                getDNDStatus: () => this.config.isDoNotDisturbTime(),
                getImportantContacts: () => this.database.getImportantContacts()
            });

            // Initialize processors
            this.messageProcessor = new MessageProcessor(this.database, this.config, logger);
            this.responseSystem = new ResponseSystem(this.database, this.config, logger);
            this.summaryGenerator = new SummaryGenerator(this.database, this.telegramBot, logger, this.config);

            // Initialize WhatsApp client
            this.client = new Client({
                authStrategy: new LocalAuth({
                    dataPath: './sessions'
                }),
                puppeteer: {
                    headless: true,
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--single-process',
                        '--disable-gpu'
                    ]
                }
            });

            this.setupWhatsAppEventHandlers();
            this.setupScheduledTasks();

            // Start WhatsApp client
            await this.client.initialize();

            logger.info('WhatsApp Bot initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize WhatsApp Bot:', error);
            throw error;
        }
    }

    setupWhatsAppEventHandlers() {
        // QR Code generation
        this.client.on('qr', (qr) => {
            logger.info('QR Code received, scan with your phone');
            qrcode.generate(qr, { small: true });
            
            // Send QR code to Telegram
            this.telegramBot?.sendMessage('🔗 WhatsApp QR Code generated. Please scan with your phone to authenticate.');
        });

        // Authentication success
        this.client.on('authenticated', () => {
            logger.info('WhatsApp client authenticated');
            this.telegramBot?.sendMessage('✅ WhatsApp client authenticated successfully');
        });

        // Client ready
        this.client.on('ready', async () => {
            logger.info('WhatsApp client is ready');
            this.isReady = true;
            this.telegramBot?.sendMessage('🚀 WhatsApp Bot is ready and monitoring messages');
        });

        // Message received
        this.client.on('message', async (message) => {
            try {
                await this.handleIncomingMessage(message);
            } catch (error) {
                logger.error('Error handling incoming message:', error);
            }
        });

        // Disconnection
        this.client.on('disconnected', (reason) => {
            logger.warn('WhatsApp client disconnected:', reason);
            this.isReady = false;
            this.telegramBot?.sendMessage(`⚠️ WhatsApp client disconnected: ${reason}`);
        });

        // Authentication failure
        this.client.on('auth_failure', (msg) => {
            logger.error('WhatsApp authentication failed:', msg);
            this.telegramBot?.sendMessage(`❌ WhatsApp authentication failed: ${msg}`);
        });
    }

    async handleIncomingMessage(message) {
        if (!this.isReady || message.fromMe) return;

        logger.info(`New message from ${message.from}: ${message.body}`);

        // Process and store message
        const processedMessage = await this.messageProcessor.processMessage(message);
        
        // Check if automatic response is needed
        const shouldRespond = await this.responseSystem.shouldAutoRespond(processedMessage);
        
        if (shouldRespond.respond) {
            const response = await this.responseSystem.generateResponse(processedMessage, shouldRespond.reason);
            if (response) {
                await message.reply(response);
                logger.info(`Auto-responded to ${message.from}: ${response}`);
                
                // Log the auto-response
                await this.database.logAutoResponse(processedMessage.id, response, shouldRespond.reason);
            }
        }

        // Check if message needs attention
        const needsAttention = await this.messageProcessor.needsAttention(processedMessage);
        
        if (needsAttention.needs) {
            await this.telegramBot.sendAttentionAlert(processedMessage, needsAttention.reason);
        }
    }

    setupScheduledTasks() {
        // Daily summary at 8:00 PM
        cron.schedule('0 20 * * *', async () => {
            try {
                logger.info('Generating daily summary...');
                await this.summaryGenerator.generateDailySummary();
            } catch (error) {
                logger.error('Error generating daily summary:', error);
            }
        }, {
            timezone: this.config.timezone || 'Europe/Rome'
        });

        // Cleanup old messages (weekly)
        cron.schedule('0 2 * * 0', async () => {
            try {
                logger.info('Cleaning up old messages...');
                await this.database.cleanupOldMessages(30); // Keep 30 days
            } catch (error) {
                logger.error('Error cleaning up old messages:', error);
            }
        });
    }

    async getSystemStatus() {
        try {
            const stats = await this.messageProcessor.getMessageStats(1); // Today's stats
            const autoResponseStats = await this.responseSystem.getResponseStats(1);

            return {
                whatsapp: this.isReady,
                database: !!this.database,
                todayMessages: stats.totalMessages,
                autoResponses: stats.autoResponses,
                attentionAlerts: stats.urgentMessages,
                uniqueContacts: stats.uniqueContacts,
                autoResponseEnabled: await this.database.getSetting('auto_response_enabled') !== 'false',
                businessHoursActive: this.config.isBusinessHours(),
                dndMode: this.config.isDoNotDisturbTime()
            };
        } catch (error) {
            logger.error('Error getting system status:', error);
            return {
                whatsapp: this.isReady,
                database: !!this.database,
                todayMessages: 0,
                autoResponses: 0,
                attentionAlerts: 0,
                uniqueContacts: 0,
                autoResponseEnabled: true,
                businessHoursActive: false,
                dndMode: false
            };
        }
    }

    async toggleDNDMode() {
        try {
            const currentStatus = this.config.doNotDisturbMode.enabled;
            this.config.doNotDisturbMode.enabled = !currentStatus;

            // Save to database instead of config file to avoid restart loop
            await this.database.setSetting('dnd_mode_enabled', (!currentStatus).toString());

            logger.info(`DND mode ${!currentStatus ? 'enabled' : 'disabled'}`);
            return !currentStatus;
        } catch (error) {
            logger.error('Error toggling DND mode:', error);
            return this.config.doNotDisturbMode.enabled;
        }
    }

    async getContactStats() {
        try {
            const topContacts = await this.messageProcessor.getTopContacts(7, 5);
            let stats = '';

            topContacts.forEach((contact, index) => {
                const name = contact.name || contact.from_contact.split('@')[0];
                stats += `${index + 1}. ${name}: ${contact.message_count} messages\n`;
            });

            return stats || 'No contact activity in the last 7 days.';
        } catch (error) {
            logger.error('Error getting contact stats:', error);
            return 'Error retrieving contact statistics.';
        }
    }

    async shutdown() {
        logger.info('Shutting down WhatsApp Bot...');

        if (this.client) {
            await this.client.destroy();
        }

        if (this.telegramBot) {
            this.telegramBot.stop();
        }

        if (this.database) {
            await this.database.close();
        }

        logger.info('WhatsApp Bot shutdown complete');
    }
}

// Main execution
async function main() {
    const bot = new WhatsAppBot();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        logger.info('Received SIGINT, shutting down gracefully...');
        await bot.shutdown();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        logger.info('Received SIGTERM, shutting down gracefully...');
        await bot.shutdown();
        process.exit(0);
    });

    try {
        await bot.initialize();
    } catch (error) {
        logger.error('Failed to start WhatsApp Bot:', error);
        process.exit(1);
    }
}

// Start the bot
if (require.main === module) {
    main().catch(console.error);
}

module.exports = WhatsAppBot;
