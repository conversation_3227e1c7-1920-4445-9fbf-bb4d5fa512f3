#!/usr/bin/env node

/**
 * Toggle Auto Response Script
 * Enables/disables auto response globally
 */

const Database = require('../src/database/database');
const path = require('path');

async function toggleAutoResponse() {
    const db = new Database();
    await db.initialize();
    
    try {
        // Get current status
        const currentStatus = await db.getSetting('auto_response_enabled');
        const isEnabled = currentStatus !== 'false';
        
        // Toggle status
        const newStatus = !isEnabled;
        await db.setSetting('auto_response_enabled', newStatus.toString());
        
        console.log('🤖 Auto Response Status Changed');
        console.log('================================');
        console.log(`Previous: ${isEnabled ? '🟢 Enabled' : '🔴 Disabled'}`);
        console.log(`Current:  ${newStatus ? '🟢 Enabled' : '🔴 Disabled'}`);
        console.log('');
        
        if (newStatus) {
            console.log('✅ Auto response is now ENABLED');
            console.log('   The bot will automatically respond to messages based on patterns');
        } else {
            console.log('❌ Auto response is now DISABLED');
            console.log('   The bot will only analyze messages but not send automatic responses');
        }
        
    } catch (error) {
        console.error('❌ Error toggling auto response:', error.message);
    } finally {
        await db.close();
    }
}

async function checkStatus() {
    const db = new Database();
    await db.initialize();
    
    try {
        const currentStatus = await db.getSetting('auto_response_enabled');
        const isEnabled = currentStatus !== 'false';
        
        console.log('🤖 Auto Response Status');
        console.log('========================');
        console.log(`Status: ${isEnabled ? '🟢 Enabled' : '🔴 Disabled'}`);
        
        if (isEnabled) {
            console.log('✅ The bot will automatically respond to messages');
        } else {
            console.log('❌ The bot will NOT send automatic responses');
        }
        
    } catch (error) {
        console.error('❌ Error checking status:', error.message);
    } finally {
        await db.close();
    }
}

// Main execution
const command = process.argv[2];

switch (command) {
    case 'toggle':
        toggleAutoResponse();
        break;
    case 'status':
        checkStatus();
        break;
    case 'enable':
        (async () => {
            const db = new Database();
            await db.initialize();
            await db.setSetting('auto_response_enabled', 'true');
            console.log('✅ Auto response ENABLED');
            await db.close();
        })();
        break;
    case 'disable':
        (async () => {
            const db = new Database();
            await db.initialize();
            await db.setSetting('auto_response_enabled', 'false');
            console.log('❌ Auto response DISABLED');
            await db.close();
        })();
        break;
    default:
        console.log('🤖 Auto Response Control');
        console.log('=========================');
        console.log('');
        console.log('Usage:');
        console.log('  node scripts/toggle-auto-response.js [command]');
        console.log('');
        console.log('Commands:');
        console.log('  toggle   - Toggle auto response on/off');
        console.log('  status   - Check current status');
        console.log('  enable   - Enable auto response');
        console.log('  disable  - Disable auto response');
        console.log('');
        console.log('Examples:');
        console.log('  npm run auto-response toggle');
        console.log('  npm run auto-response status');
        break;
}
