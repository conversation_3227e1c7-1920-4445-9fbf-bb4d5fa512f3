/**
 * Summary Generator
 * Creates AI-enhanced daily summaries and reports for Telegram notifications
 */

const OpenAIService = require('../ai/openaiService');

class SummaryGenerator {
    constructor(database, telegramBot, logger, config) {
        this.database = database;
        this.telegramBot = telegramBot;
        this.logger = logger;
        this.config = config;
        this.aiService = new OpenAIService(config, database, logger);
    }

    async generateDailySummary(date = null) {
        try {
            const targetDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

            this.logger.info(`Generating daily summary for ${targetDate}`);

            // Get summary data
            const summaryData = await this.collectDailySummaryData(targetDate);

            // Try AI-enhanced summary first
            if (this.config.ai?.features?.intelligentSummaries && this.aiService.isEnabled) {
                try {
                    const aiSummary = await this.generateAISummary(summaryData, targetDate);
                    summaryData.aiSummary = aiSummary;
                    summaryData.isAIGenerated = true;
                } catch (error) {
                    this.logger.warn('AI summary failed, using traditional format:', error.message);
                    summaryData.isAIGenerated = false;
                }
            }

            // Generate traditional summary as fallback
            if (!summaryData.aiSummary) {
                summaryData.summary = this.formatDailySummary(summaryData);
                summaryData.isAIGenerated = false;
            }

            // Save to database
            await this.database.saveDailySummary(targetDate, {
                total_messages: summaryData.totalMessages,
                unique_contacts: summaryData.uniqueContacts,
                urgent_messages: summaryData.urgentMessages,
                auto_responses: summaryData.autoResponses,
                summary_text: summaryData.aiSummary?.content || summaryData.summary,
                is_ai_generated: summaryData.isAIGenerated
            });

            // Send to Telegram
            await this.telegramBot.sendDailySummary(summaryData);

            this.logger.info(`Daily summary generated and sent for ${targetDate}`);
            return summaryData;

        } catch (error) {
            this.logger.error('Error generating daily summary:', error);
            throw error;
        }
    }

    async generateAISummary(summaryData, date) {
        try {
            // Get all messages for the day for AI analysis
            const startOfDay = new Date(date + 'T00:00:00.000Z').getTime();
            const endOfDay = new Date(date + 'T23:59:59.999Z').getTime();

            const messages = await this.database.all(`
                SELECT m.*, c.name as contact_name, c.profile_type
                FROM messages m
                LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
                WHERE m.timestamp >= ? AND m.timestamp <= ?
                ORDER BY m.timestamp ASC
            `, [startOfDay, endOfDay]);

            if (messages.length === 0) {
                return null;
            }

            // Perform sentiment analysis on messages
            const sentimentPromises = messages.map(async (msg) => {
                const sentiment = await this.aiService.analyzeSentiment(msg.body);
                return { ...msg, sentiment: sentiment.sentiment };
            });

            const messagesWithSentiment = await Promise.all(sentimentPromises);

            // Generate AI summary
            const aiResult = await this.aiService.generateSummary(messagesWithSentiment, 'daily');

            // Extract insights
            const insights = await this.extractAIInsights(messagesWithSentiment);

            return {
                content: aiResult.content,
                insights: insights,
                cost: aiResult.cost,
                model: aiResult.model,
                sentimentBreakdown: this.analyzeSentimentBreakdown(messagesWithSentiment)
            };

        } catch (error) {
            this.logger.error('AI summary generation failed:', error);
            throw error;
        }
    }

    async extractAIInsights(messages) {
        const insights = {
            actionItems: [],
            deadlines: [],
            fileRequests: [],
            meetingRequests: [],
            concerningMessages: []
        };

        for (const message of messages) {
            const body = message.body.toLowerCase();

            // Extract action items
            if (body.includes('need to') || body.includes('should') || body.includes('must')) {
                insights.actionItems.push({
                    contact: message.contact_name || message.from_contact,
                    message: message.body,
                    timestamp: message.timestamp
                });
            }

            // Extract deadlines
            if (body.includes('deadline') || body.includes('by ') || body.includes('before ')) {
                insights.deadlines.push({
                    contact: message.contact_name || message.from_contact,
                    message: message.body,
                    timestamp: message.timestamp
                });
            }

            // Extract file requests
            if (body.includes('send') && (body.includes('file') || body.includes('document'))) {
                insights.fileRequests.push({
                    contact: message.contact_name || message.from_contact,
                    message: message.body,
                    timestamp: message.timestamp
                });
            }

            // Extract meeting requests
            if (body.includes('meeting') || body.includes('call') || body.includes('schedule')) {
                insights.meetingRequests.push({
                    contact: message.contact_name || message.from_contact,
                    message: message.body,
                    timestamp: message.timestamp
                });
            }

            // Identify concerning messages
            if (message.sentiment === 'negative' || message.needs_attention) {
                insights.concerningMessages.push({
                    contact: message.contact_name || message.from_contact,
                    message: message.body,
                    reason: message.attention_reason || 'Negative sentiment',
                    timestamp: message.timestamp
                });
            }
        }

        return insights;
    }

    analyzeSentimentBreakdown(messages) {
        const breakdown = { positive: 0, negative: 0, neutral: 0 };

        messages.forEach(msg => {
            if (msg.sentiment) {
                breakdown[msg.sentiment]++;
            }
        });

        const total = messages.length;
        return {
            positive: { count: breakdown.positive, percentage: ((breakdown.positive / total) * 100).toFixed(1) },
            negative: { count: breakdown.negative, percentage: ((breakdown.negative / total) * 100).toFixed(1) },
            neutral: { count: breakdown.neutral, percentage: ((breakdown.neutral / total) * 100).toFixed(1) }
        };
    }

    async collectDailySummaryData(date) {
        const startOfDay = new Date(date + 'T00:00:00.000Z').getTime();
        const endOfDay = new Date(date + 'T23:59:59.999Z').getTime();

        const summaryData = {
            date: date,
            totalMessages: 0,
            uniqueContacts: 0,
            urgentMessages: 0,
            autoResponses: 0,
            contactsSummary: '',
            requestsSummary: '',
            urgentSummary: '',
            unhandledSummary: ''
        };

        try {
            // Get total messages
            const totalResult = await this.database.get(`
                SELECT COUNT(*) as count 
                FROM messages 
                WHERE timestamp >= ? AND timestamp <= ?
            `, [startOfDay, endOfDay]);
            summaryData.totalMessages = totalResult.count;

            // Get unique contacts
            const contactsResult = await this.database.get(`
                SELECT COUNT(DISTINCT from_contact) as count 
                FROM messages 
                WHERE timestamp >= ? AND timestamp <= ?
            `, [startOfDay, endOfDay]);
            summaryData.uniqueContacts = contactsResult.count;

            // Get urgent messages
            const urgentResult = await this.database.get(`
                SELECT COUNT(*) as count 
                FROM messages 
                WHERE timestamp >= ? AND timestamp <= ? AND needs_attention = 1
            `, [startOfDay, endOfDay]);
            summaryData.urgentMessages = urgentResult.count;

            // Get auto responses
            const autoResponseResult = await this.database.get(`
                SELECT COUNT(*) as count 
                FROM auto_responses ar
                JOIN messages m ON ar.message_id = m.id
                WHERE m.timestamp >= ? AND m.timestamp <= ?
            `, [startOfDay, endOfDay]);
            summaryData.autoResponses = autoResponseResult.count;

            // Get top contacts
            const topContacts = await this.database.all(`
                SELECT 
                    m.from_contact,
                    c.name,
                    COUNT(*) as message_count,
                    MAX(m.timestamp) as last_message
                FROM messages m
                LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
                WHERE m.timestamp >= ? AND m.timestamp <= ?
                GROUP BY m.from_contact
                ORDER BY message_count DESC
                LIMIT 5
            `, [startOfDay, endOfDay]);

            summaryData.contactsSummary = this.formatContactsSummary(topContacts);

            // Get requests and follow-ups
            const requests = await this.database.all(`
                SELECT 
                    m.body,
                    c.name,
                    m.from_contact,
                    m.timestamp
                FROM messages m
                LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
                WHERE m.timestamp >= ? AND m.timestamp <= ?
                AND (
                    LOWER(m.body) LIKE '%can you send%' OR
                    LOWER(m.body) LIKE '%please send%' OR
                    LOWER(m.body) LIKE '%need the%' OR
                    LOWER(m.body) LIKE '%where is%' OR
                    LOWER(m.body) LIKE '%when%' OR
                    LOWER(m.body) LIKE '%meeting%'
                )
                ORDER BY m.timestamp DESC
                LIMIT 10
            `, [startOfDay, endOfDay]);

            summaryData.requestsSummary = this.formatRequestsSummary(requests);

            // Get urgent messages details
            const urgentMessages = await this.database.all(`
                SELECT 
                    m.body,
                    c.name,
                    m.from_contact,
                    m.attention_reason,
                    m.timestamp
                FROM messages m
                LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
                WHERE m.timestamp >= ? AND m.timestamp <= ? AND m.needs_attention = 1
                ORDER BY m.timestamp DESC
                LIMIT 5
            `, [startOfDay, endOfDay]);

            summaryData.urgentSummary = this.formatUrgentSummary(urgentMessages);

            // Get unhandled items (messages needing attention that haven't been processed)
            const unhandledMessages = await this.database.all(`
                SELECT 
                    m.body,
                    c.name,
                    m.from_contact,
                    m.attention_reason,
                    m.timestamp
                FROM messages m
                LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
                WHERE m.needs_attention = 1 AND m.processed = 0
                AND m.timestamp >= ? - 86400000  -- Include yesterday's unhandled items
                ORDER BY m.timestamp DESC
                LIMIT 10
            `, [startOfDay]);

            summaryData.unhandledSummary = this.formatUnhandledSummary(unhandledMessages);

        } catch (error) {
            this.logger.error('Error collecting daily summary data:', error);
        }

        return summaryData;
    }

    formatContactsSummary(contacts) {
        if (!contacts.length) return '';

        return contacts.map(contact => {
            const name = contact.name || contact.from_contact.split('@')[0];
            return `• ${name}: ${contact.message_count} message${contact.message_count > 1 ? 's' : ''}`;
        }).join('\n');
    }

    formatRequestsSummary(requests) {
        if (!requests.length) return '';

        return requests.map(request => {
            const name = request.name || request.from_contact.split('@')[0];
            const preview = request.body.length > 50 
                ? request.body.substring(0, 50) + '...' 
                : request.body;
            const time = new Date(request.timestamp).toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            return `• ${name} (${time}): ${preview}`;
        }).join('\n');
    }

    formatUrgentSummary(urgentMessages) {
        if (!urgentMessages.length) return '';

        return urgentMessages.map(message => {
            const name = message.name || message.from_contact.split('@')[0];
            const preview = message.body.length > 60 
                ? message.body.substring(0, 60) + '...' 
                : message.body;
            const time = new Date(message.timestamp).toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            return `• ${name} (${time}): ${preview}\n  Reason: ${message.attention_reason}`;
        }).join('\n\n');
    }

    formatUnhandledSummary(unhandledMessages) {
        if (!unhandledMessages.length) return '';

        return unhandledMessages.map(message => {
            const name = message.name || message.from_contact.split('@')[0];
            const preview = message.body.length > 50 
                ? message.body.substring(0, 50) + '...' 
                : message.body;
            const age = Math.floor((Date.now() - message.timestamp) / (1000 * 60 * 60));
            return `• ${name} (${age}h ago): ${preview}`;
        }).join('\n');
    }

    formatDailySummary(summaryData) {
        let summary = `Daily WhatsApp Summary for ${summaryData.date}\n\n`;
        
        summary += `Statistics:\n`;
        summary += `- Total messages: ${summaryData.totalMessages}\n`;
        summary += `- Unique contacts: ${summaryData.uniqueContacts}\n`;
        summary += `- Auto responses: ${summaryData.autoResponses}\n`;
        summary += `- Urgent messages: ${summaryData.urgentMessages}\n\n`;

        if (summaryData.contactsSummary) {
            summary += `Most Active Contacts:\n${summaryData.contactsSummary}\n\n`;
        }

        if (summaryData.requestsSummary) {
            summary += `Requests & Questions:\n${summaryData.requestsSummary}\n\n`;
        }

        if (summaryData.urgentSummary) {
            summary += `Urgent Messages:\n${summaryData.urgentSummary}\n\n`;
        }

        if (summaryData.unhandledSummary) {
            summary += `Unhandled Items:\n${summaryData.unhandledSummary}\n\n`;
        }

        return summary;
    }

    async generateWeeklySummary() {
        try {
            const endDate = new Date();
            const startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000));
            
            const weeklyData = await this.collectWeeklySummaryData(startDate, endDate);
            
            await this.telegramBot.sendMessage(`
📊 *Weekly WhatsApp Summary*
📅 ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}

📈 *Statistics:*
• Total messages: ${weeklyData.totalMessages}
• Unique contacts: ${weeklyData.uniqueContacts}
• Auto responses: ${weeklyData.autoResponses}
• Urgent messages: ${weeklyData.urgentMessages}
• Average messages/day: ${Math.round(weeklyData.totalMessages / 7)}

👥 *Top Contacts:*
${weeklyData.topContacts}

📋 *Response Patterns:*
${weeklyData.responsePatterns}
            `, { parse_mode: 'Markdown' });

        } catch (error) {
            this.logger.error('Error generating weekly summary:', error);
        }
    }

    async collectWeeklySummaryData(startDate, endDate) {
        const startTimestamp = startDate.getTime();
        const endTimestamp = endDate.getTime();

        // Similar to daily summary but for a week
        // Implementation would be similar to collectDailySummaryData
        // but with different time range and aggregations
        
        return {
            totalMessages: 0,
            uniqueContacts: 0,
            autoResponses: 0,
            urgentMessages: 0,
            topContacts: '',
            responsePatterns: ''
        };
    }

    async generateContactSummary(contactId, days = 30) {
        try {
            const timestamp = Date.now() - (days * 24 * 60 * 60 * 1000);
            
            const contact = await this.database.getContact(contactId);
            const messages = await this.database.getRecentMessages(days * 24, contactId);
            
            const summary = {
                contact: contact,
                messageCount: messages.length,
                lastMessage: messages[0],
                urgentMessages: messages.filter(m => m.needs_attention).length,
                responseRate: 0 // Calculate based on auto responses
            };

            return summary;

        } catch (error) {
            this.logger.error('Error generating contact summary:', error);
            return null;
        }
    }
}

module.exports = SummaryGenerator;
