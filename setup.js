#!/usr/bin/env node

/**
 * Setup Script for WhatsApp Intelligent Bot
 * Helps users configure the bot for first-time use
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setup() {
    console.log('🤖 WhatsApp Intelligent Bot Setup\n');
    console.log('This script will help you configure your bot for first-time use.\n');

    // Check if .env already exists
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
        const overwrite = await question('.env file already exists. Overwrite? (y/N): ');
        if (overwrite.toLowerCase() !== 'y') {
            console.log('Setup cancelled. You can manually edit .env file.');
            rl.close();
            return;
        }
    }

    console.log('📱 First, you need to create a Telegram bot:');
    console.log('1. Message @BotFather on Telegram');
    console.log('2. Send /newbot and follow the instructions');
    console.log('3. Copy the bot token\n');

    const botToken = await question('Enter your Telegram bot token: ');
    if (!botToken.trim()) {
        console.log('❌ Bot token is required. Please run setup again.');
        rl.close();
        return;
    }

    console.log('\n🆔 Now you need your Telegram chat ID:');
    console.log('1. Message @userinfobot on Telegram');
    console.log('2. Copy your chat ID (the number)\n');

    const chatId = await question('Enter your Telegram chat ID: ');
    if (!chatId.trim()) {
        console.log('❌ Chat ID is required. Please run setup again.');
        rl.close();
        return;
    }

    // Optional OpenAI API key
    console.log('\n🤖 OpenAI API Key (optional - for advanced AI responses):');
    console.log('Leave empty if you don\'t want to use OpenAI features.\n');
    const openaiKey = await question('Enter your OpenAI API key (optional): ');

    // Create .env file
    const envContent = `# WhatsApp Bot Configuration
# Generated by setup script

# Telegram Bot Configuration (Required)
TELEGRAM_BOT_TOKEN=${botToken}
TELEGRAM_CHAT_ID=${chatId}

# Database Configuration
DB_PATH=./data/whatsapp_bot.db

# Timezone
TIMEZONE=Europe/Rome

${openaiKey ? `# OpenAI Configuration
OPENAI_API_KEY=${openaiKey}
OPENAI_MODEL=gpt-3.5-turbo` : '# OpenAI Configuration (Optional)\n# OPENAI_API_KEY=your_openai_api_key_here\n# OPENAI_MODEL=gpt-3.5-turbo'}

# Logging Level
LOG_LEVEL=info
`;

    fs.writeFileSync(envPath, envContent);
    console.log('\n✅ .env file created successfully!');

    // Create directories if they don't exist
    const dirs = ['data', 'logs', 'config', 'sessions'];
    dirs.forEach(dir => {
        const dirPath = path.join(__dirname, dir);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`📁 Created directory: ${dir}/`);
        }
    });

    // Create default user config if it doesn't exist
    const userConfigPath = path.join(__dirname, 'config', 'user-config.json');
    if (!fs.existsSync(userConfigPath)) {
        const defaultConfig = {
            userProfiles: {},
            responsePatterns: {
                greetings: {
                    patterns: ["^(hi|hello|hey|ciao|salve)", "^good (morning|afternoon|evening)"],
                    responses: [
                        "Hi! I'm currently not available. I'll get back to you as soon as possible.",
                        "Hello! Thanks for your message. I'll respond shortly.",
                        "Hey! I'm away right now but will reply soon."
                    ]
                },
                fileRequests: {
                    patterns: ["can you send (me )?the", "please send", "need the (file|document|report)", "where is the"],
                    responses: [
                        "I've noted your request for the file. I'll send it to you shortly.",
                        "Thanks for the request. I'll get that file to you as soon as I can."
                    ]
                }
            },
            importantContacts: [],
            urgencyKeywords: ["urgent", "emergency", "asap", "immediately", "critical", "problem", "issue", "blocked", "help", "stuck", "deadline"],
            businessHours: {
                enabled: true,
                timezone: "Europe/Rome",
                schedule: {
                    monday: { start: "09:00", end: "18:00" },
                    tuesday: { start: "09:00", end: "18:00" },
                    wednesday: { start: "09:00", end: "18:00" },
                    thursday: { start: "09:00", end: "18:00" },
                    friday: { start: "09:00", end: "18:00" },
                    saturday: null,
                    sunday: null
                },
                outOfHoursResponse: "Thanks for your message! I'm currently outside business hours (9 AM - 6 PM, Mon-Fri). I'll get back to you during business hours."
            },
            doNotDisturbMode: {
                enabled: false,
                schedule: { start: "22:00", end: "08:00" },
                autoResponse: "I'm currently in Do Not Disturb mode. I'll respond to your message tomorrow morning.",
                allowUrgent: true,
                allowImportantContacts: true
            }
        };

        fs.writeFileSync(userConfigPath, JSON.stringify(defaultConfig, null, 2));
        console.log('⚙️ Created default user configuration');
    }

    console.log('\n🎉 Setup complete! You can now start the bot:');
    console.log('   npm run dev');
    console.log('\n📱 When you start the bot:');
    console.log('1. A QR code will appear in the terminal');
    console.log('2. Scan it with WhatsApp on your phone');
    console.log('3. You\'ll receive a confirmation message on Telegram');
    console.log('\n📚 Check README.md for more configuration options and usage instructions.');

    rl.close();
}

// Handle errors
process.on('unhandledRejection', (error) => {
    console.error('❌ Setup failed:', error.message);
    rl.close();
    process.exit(1);
});

// Run setup
setup().catch(console.error);
