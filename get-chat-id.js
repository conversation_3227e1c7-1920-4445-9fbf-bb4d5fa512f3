#!/usr/bin/env node

/**
 * Get Chat ID Helper Script
 * This script helps you find your Telegram chat ID
 */

require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');

const token = process.env.TELEGRAM_BOT_TOKEN;

if (!token) {
    console.error('❌ TELEGRAM_BOT_TOKEN not found in .env file');
    console.log('Please make sure your .env file contains:');
    console.log('TELEGRAM_BOT_TOKEN=your_bot_token_here');
    process.exit(1);
}

console.log('🔍 Chat ID Finder');
console.log('==================');
console.log('');
console.log('📱 Steps to get your chat ID:');
console.log('1. Start this script');
console.log('2. Send any message to your bot on Telegram');
console.log('3. Your chat ID will be displayed here');
console.log('4. Copy the chat ID to your .env file');
console.log('');
console.log('⏳ Waiting for messages...');
console.log('(Press Ctrl+C to stop)');
console.log('');

const bot = new TelegramBot(token, { polling: true });

bot.on('message', (msg) => {
    const chatId = msg.chat.id;
    const username = msg.from.username || 'N/A';
    const firstName = msg.from.first_name || 'N/A';
    const lastName = msg.from.last_name || 'N/A';
    
    console.log('✅ Message received!');
    console.log('==================');
    console.log(`📋 Your Chat ID: ${chatId}`);
    console.log(`👤 Username: @${username}`);
    console.log(`👤 Name: ${firstName} ${lastName}`);
    console.log('');
    console.log('📝 Add this to your .env file:');
    console.log(`TELEGRAM_CHAT_ID=${chatId}`);
    console.log('');
    console.log('✅ You can now stop this script (Ctrl+C) and update your .env file');
    
    // Send confirmation
    bot.sendMessage(chatId, `✅ Perfect! Your chat ID is: ${chatId}\n\nAdd this to your .env file:\nTELEGRAM_CHAT_ID=${chatId}`);
});

bot.on('polling_error', (error) => {
    console.error('❌ Polling error:', error.message);
    if (error.message.includes('401')) {
        console.log('');
        console.log('🔧 This usually means your bot token is invalid.');
        console.log('Please check your TELEGRAM_BOT_TOKEN in the .env file.');
    }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Stopping chat ID finder...');
    bot.stopPolling();
    process.exit(0);
});
