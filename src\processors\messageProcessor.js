/**
 * Message Processor
 * Analyzes and classifies incoming WhatsApp messages
 */

class MessageProcessor {
    constructor(database, config, logger) {
        this.database = database;
        this.config = config;
        this.logger = logger;
    }

    async processMessage(whatsappMessage) {
        try {
            // Extract message data
            const messageData = await this.extractMessageData(whatsappMessage);
            
            // Save to database
            const messageId = await this.database.saveMessage(messageData);
            messageData.id = messageId;

            // Analyze message content
            const analysis = await this.analyzeMessage(messageData);
            messageData.analysis = analysis;

            // Update contact information
            await this.updateContactInfo(whatsappMessage);

            this.logger.info(`Processed message ${messageId} from ${messageData.from_contact}`);
            
            return messageData;
        } catch (error) {
            this.logger.error('Error processing message:', error);
            throw error;
        }
    }

    async extractMessageData(whatsappMessage) {
        const contact = await whatsappMessage.getContact();
        const chat = await whatsappMessage.getChat();
        
        return {
            whatsapp_id: whatsappMessage.id._serialized,
            from_contact: whatsappMessage.from,
            to_contact: whatsappMessage.to,
            body: whatsappMessage.body,
            timestamp: whatsappMessage.timestamp * 1000, // Convert to milliseconds
            type: whatsappMessage.type,
            is_group: chat.isGroup,
            group_name: chat.isGroup ? chat.name : null,
            contact_name: contact.pushname || contact.name || null,
            contact_number: contact.number || null
        };
    }

    async analyzeMessage(messageData) {
        const analysis = {
            hasUrgentKeywords: false,
            urgentKeywords: [],
            messageType: 'unknown',
            isQuestion: false,
            isRequest: false,
            sentiment: 'neutral',
            topics: [],
            needsAttention: false,
            attentionReason: null
        };

        const body = messageData.body.toLowerCase();

        // Check for urgent keywords
        const urgentKeywords = this.config.urgencyKeywords || [];
        analysis.urgentKeywords = urgentKeywords.filter(keyword => 
            body.includes(keyword.toLowerCase())
        );
        analysis.hasUrgentKeywords = analysis.urgentKeywords.length > 0;

        // Classify message type
        analysis.messageType = this.classifyMessageType(body);
        
        // Check if it's a question
        analysis.isQuestion = this.isQuestion(body);
        
        // Check if it's a request
        analysis.isRequest = this.isRequest(body);

        // Determine if needs attention
        const attentionCheck = await this.needsAttention(messageData, analysis);
        analysis.needsAttention = attentionCheck.needs;
        analysis.attentionReason = attentionCheck.reason;

        return analysis;
    }

    classifyMessageType(body) {
        // Greeting patterns
        if (/^(hi|hello|hey|ciao|salve|good morning|good afternoon|good evening)/i.test(body)) {
            return 'greeting';
        }

        // File/document requests
        if (/(send|share|file|document|report|pdf|photo|image|video)/i.test(body)) {
            return 'file_request';
        }

        // Meeting/scheduling
        if (/(meeting|schedule|appointment|call|when|time|date)/i.test(body)) {
            return 'scheduling';
        }

        // Availability check
        if (/(available|free|busy|can you|do you have time)/i.test(body)) {
            return 'availability';
        }

        // Information request
        if (/(what|where|when|how|why|which|who)/i.test(body)) {
            return 'information_request';
        }

        // Confirmation/acknowledgment
        if (/^(ok|okay|thanks|thank you|got it|understood|yes|no|sure)/i.test(body)) {
            return 'acknowledgment';
        }

        return 'general';
    }

    isQuestion(body) {
        // Check for question marks
        if (body.includes('?')) return true;

        // Check for question words at the beginning
        if (/^(what|where|when|how|why|which|who|can|could|would|will|do|does|did|is|are|was|were)/i.test(body)) {
            return true;
        }

        return false;
    }

    isRequest(body) {
        const requestPatterns = [
            /can you/i,
            /could you/i,
            /please/i,
            /need/i,
            /want/i,
            /send me/i,
            /give me/i,
            /help me/i,
            /let me know/i
        ];

        return requestPatterns.some(pattern => pattern.test(body));
    }

    async needsAttention(messageData, analysis = null) {
        const reasons = [];

        // If no analysis provided, create basic one
        if (!analysis) {
            analysis = await this.analyzeMessage(messageData);
        }

        // Check for urgent keywords
        if (analysis.hasUrgentKeywords) {
            reasons.push(`Contains urgent keywords: ${analysis.urgentKeywords.join(', ')}`);
        }

        // Check if from important contact
        const contact = await this.database.getContact(messageData.from_contact);
        if (contact && contact.is_important) {
            reasons.push('Message from important contact');
        }

        // Check for unanswered questions older than X hours
        const hoursThreshold = 2;
        const messageAge = (Date.now() - messageData.timestamp) / (1000 * 60 * 60);
        if (analysis.isQuestion && messageAge > hoursThreshold) {
            reasons.push(`Unanswered question older than ${hoursThreshold} hours`);
        }

        // Check for specific request types that need attention
        if (analysis.isRequest && analysis.messageType === 'file_request') {
            reasons.push('File or document request');
        }

        // Check business hours and DND mode
        if (!this.config.isBusinessHours() && analysis.hasUrgentKeywords) {
            reasons.push('Urgent message outside business hours');
        }

        // Check for follow-up messages
        const recentMessages = await this.database.getRecentMessages(24, messageData.from_contact);
        if (recentMessages.length > 3 && analysis.isQuestion) {
            reasons.push('Multiple recent messages with questions');
        }

        // Update message in database if needs attention
        if (reasons.length > 0) {
            await this.database.run(
                'UPDATE messages SET needs_attention = 1, attention_reason = ? WHERE id = ?',
                [reasons.join('; '), messageData.id]
            );
        }

        return {
            needs: reasons.length > 0,
            reason: reasons.join('; ') || null
        };
    }

    async updateContactInfo(whatsappMessage) {
        try {
            const contact = await whatsappMessage.getContact();
            const chat = await whatsappMessage.getChat();
            
            const contactData = {
                name: contact.pushname || contact.name || null,
                phone_number: contact.number || null,
                last_seen: new Date().toISOString(),
                message_count_increment: 1
            };

            // If it's a new contact, try to determine profile type
            const existingContact = await this.database.getContact(whatsappMessage.from);
            if (!existingContact) {
                contactData.profile_type = this.determineProfileType(contact, chat);
            }

            await this.database.updateContact(whatsappMessage.from, contactData);
        } catch (error) {
            this.logger.error('Error updating contact info:', error);
        }
    }

    determineProfileType(contact, chat) {
        // Basic heuristics to determine contact type
        // This can be enhanced with more sophisticated logic
        
        if (chat.isGroup) {
            return 'group_member';
        }

        // Check if contact name suggests business relationship
        const name = (contact.pushname || contact.name || '').toLowerCase();
        if (name.includes('dr.') || name.includes('dott.') || name.includes('ing.')) {
            return 'professional';
        }

        // Default to unknown - user can manually classify later
        return 'unknown';
    }

    async getMessageStats(days = 7) {
        const timestamp = Date.now() - (days * 24 * 60 * 60 * 1000);
        
        const stats = {
            totalMessages: 0,
            uniqueContacts: 0,
            urgentMessages: 0,
            questionsAsked: 0,
            requestsMade: 0,
            autoResponses: 0
        };

        try {
            // Get total messages
            const totalResult = await this.database.get(
                'SELECT COUNT(*) as count FROM messages WHERE timestamp > ?',
                [timestamp]
            );
            stats.totalMessages = totalResult.count;

            // Get unique contacts
            const contactsResult = await this.database.get(
                'SELECT COUNT(DISTINCT from_contact) as count FROM messages WHERE timestamp > ?',
                [timestamp]
            );
            stats.uniqueContacts = contactsResult.count;

            // Get urgent messages
            const urgentResult = await this.database.get(
                'SELECT COUNT(*) as count FROM messages WHERE timestamp > ? AND needs_attention = 1',
                [timestamp]
            );
            stats.urgentMessages = urgentResult.count;

            // Get auto responses
            const autoResponseResult = await this.database.get(`
                SELECT COUNT(*) as count FROM auto_responses ar
                JOIN messages m ON ar.message_id = m.id
                WHERE m.timestamp > ?
            `, [timestamp]);
            stats.autoResponses = autoResponseResult.count;

        } catch (error) {
            this.logger.error('Error getting message stats:', error);
        }

        return stats;
    }

    async getTopContacts(days = 7, limit = 10) {
        const timestamp = Date.now() - (days * 24 * 60 * 60 * 1000);
        
        try {
            return await this.database.all(`
                SELECT 
                    m.from_contact,
                    c.name,
                    COUNT(*) as message_count,
                    MAX(m.timestamp) as last_message
                FROM messages m
                LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
                WHERE m.timestamp > ?
                GROUP BY m.from_contact
                ORDER BY message_count DESC
                LIMIT ?
            `, [timestamp, limit]);
        } catch (error) {
            this.logger.error('Error getting top contacts:', error);
            return [];
        }
    }
}

module.exports = MessageProcessor;
