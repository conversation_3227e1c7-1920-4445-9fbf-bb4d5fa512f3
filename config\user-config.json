{"userProfiles": {}, "responsePatterns": {"greetings": {"patterns": [{}, {}], "responses": ["Hi! I'm currently not available. I'll get back to you as soon as possible.", "Hello! Thanks for your message. I'll respond shortly.", "Hey! I'm away right now but will reply soon."]}, "fileRequests": {"patterns": [{}, {}, {}, {}], "responses": ["I've noted your request for the file. I'll send it to you shortly.", "Thanks for the request. I'll get that file to you as soon as I can.", "Got it! I'll send you the document you requested."]}, "meetingQuestions": {"patterns": [{}, {}, {}, {}], "responses": ["Let me check the calendar and get back to you with the meeting details.", "I'll confirm the meeting time and send you the details shortly.", "Thanks for asking! I'll send you the meeting information in a moment."]}, "availability": {"patterns": [{}, {}, {}], "responses": ["I'm not available right now, but I'll get back to you soon to discuss.", "Currently busy, but I'll reach out to you shortly to chat.", "Not available at the moment, but let's connect soon!"]}}, "importantContacts": [], "urgencyKeywords": ["urgent", "emergency", "asap", "immediately", "critical", "problem", "issue", "blocked", "help", "stuck", "deadline", "today", "now", "urgente", "emergenza", "subito", "problema", "aiuto", "bloccato"], "businessHours": {"enabled": true, "timezone": "Europe/Rome", "schedule": {"monday": {"start": "09:00", "end": "18:00"}, "tuesday": {"start": "09:00", "end": "18:00"}, "wednesday": {"start": "09:00", "end": "18:00"}, "thursday": {"start": "09:00", "end": "18:00"}, "friday": {"start": "09:00", "end": "18:00"}, "saturday": null, "sunday": null}, "outOfHoursResponse": "Thanks for your message! I'm currently outside business hours (9 AM - 6 PM, Mon-Fri). I'll get back to you during business hours."}, "doNotDisturbMode": {"enabled": false, "schedule": {"start": "22:00", "end": "08:00"}, "autoResponse": "I'm currently in Do Not Disturb mode. I'll respond to your message tomorrow morning.", "allowUrgent": true, "allowImportantContacts": true}}