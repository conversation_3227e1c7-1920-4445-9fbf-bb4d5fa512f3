#!/usr/bin/env node

/**
 * Test Script for WhatsApp Intelligent Bot
 * Tests core functionality without requiring WhatsApp connection
 */

const path = require('path');
require('dotenv').config();

// Import modules
const Database = require('./src/database/database');
const Config = require('./src/config/config');
const MessageProcessor = require('./src/processors/messageProcessor');
const ResponseSystem = require('./src/responses/responseSystem');
const SummaryGenerator = require('./src/summary/summaryGenerator');
const OpenAIService = require('./src/ai/openaiService');

// Mock logger
const logger = {
    info: console.log,
    error: console.error,
    warn: console.warn
};

// Mock Telegram bot
const mockTelegramBot = {
    sendMessage: async (text) => {
        console.log('📱 Telegram Message:', text);
    },
    sendDailySummary: async (data) => {
        console.log('📊 Daily Summary:', JSON.stringify(data, null, 2));
    },
    sendAttentionAlert: async (messageData, reason) => {
        console.log('⚠️ Attention Alert:', { messageData, reason });
    }
};

async function testDatabase() {
    console.log('\n🗄️ Testing Database...');
    
    const db = new Database('./data/test_whatsapp_bot.db');
    await db.initialize();
    
    // Test saving a message
    const testMessage = {
        whatsapp_id: 'test_message_1',
        from_contact: '<EMAIL>',
        to_contact: '<EMAIL>',
        body: 'Hello, this is a test message',
        timestamp: Date.now(),
        type: 'text',
        is_group: false,
        group_name: null,
        needs_attention: false,
        attention_reason: null
    };
    
    const messageId = await db.saveMessage(testMessage);
    console.log('✅ Message saved with ID:', messageId);
    
    // Test getting recent messages
    const recentMessages = await db.getRecentMessages(24);
    console.log('✅ Recent messages count:', recentMessages.length);
    
    await db.close();
    console.log('✅ Database test completed');
}

async function testConfig() {
    console.log('\n⚙️ Testing Configuration...');

    try {
        // Set test environment variables
        process.env.TELEGRAM_BOT_TOKEN = 'test_token';
        process.env.TELEGRAM_CHAT_ID = 'test_chat_id';

        const config = new Config();

        console.log('✅ Config loaded');
        console.log('- Business hours enabled:', config.businessHours.enabled);
        console.log('- DND mode enabled:', config.doNotDisturbMode.enabled);
        console.log('- Response patterns count:', Object.keys(config.responsePatterns).length);
        console.log('- Urgency keywords count:', config.urgencyKeywords.length);

        // Test business hours check
        const isBusinessHours = config.isBusinessHours();
        console.log('- Currently business hours:', isBusinessHours);

        // Test DND check
        const isDND = config.isDoNotDisturbTime();
        console.log('- Currently DND time:', isDND);

        console.log('✅ Configuration test completed');
    } catch (error) {
        console.error('❌ Configuration test failed:', error.message);
    }
}

async function testMessageProcessor() {
    console.log('\n🔍 Testing Message Processor...');
    
    const db = new Database('./data/test_whatsapp_bot.db');
    await db.initialize();
    
    const config = new Config();
    const processor = new MessageProcessor(db, config, logger);
    
    // Test message analysis
    const testMessages = [
        {
            id: 1,
            body: 'Hi there! How are you?',
            from_contact: '<EMAIL>',
            timestamp: Date.now()
        },
        {
            id: 2,
            body: 'URGENT: Can you send me the report immediately?',
            from_contact: '<EMAIL>',
            timestamp: Date.now()
        },
        {
            id: 3,
            body: 'When is our meeting scheduled?',
            from_contact: '<EMAIL>',
            timestamp: Date.now()
        }
    ];
    
    for (const message of testMessages) {
        const analysis = await processor.analyzeMessage(message);
        console.log(`✅ Message "${message.body.substring(0, 30)}..."`);
        console.log(`   Type: ${analysis.messageType}`);
        console.log(`   Is Question: ${analysis.isQuestion}`);
        console.log(`   Is Request: ${analysis.isRequest}`);
        console.log(`   Has Urgent Keywords: ${analysis.hasUrgentKeywords}`);
        console.log(`   Needs Attention: ${analysis.needsAttention}`);
    }
    
    await db.close();
    console.log('✅ Message Processor test completed');
}

async function testResponseSystem() {
    console.log('\n🤖 Testing Response System...');
    
    const db = new Database('./data/test_whatsapp_bot.db');
    await db.initialize();
    
    const config = new Config();
    const responseSystem = new ResponseSystem(db, config, logger);
    
    // Test response pattern matching
    const testMessages = [
        'Hi there!',
        'Can you send me the file?',
        'When is our meeting?',
        'Are you available?',
        'This is urgent!'
    ];
    
    for (const body of testMessages) {
        const pattern = responseSystem.matchResponsePattern(body);
        console.log(`✅ Message: "${body}" -> Pattern: ${pattern || 'none'}`);
        
        if (pattern) {
            const response = config.getRandomResponse(pattern);
            console.log(`   Response: "${response}"`);
        }
    }
    
    await db.close();
    console.log('✅ Response System test completed');
}

async function testSummaryGenerator() {
    console.log('\n📊 Testing Summary Generator...');

    const db = new Database('./data/test_whatsapp_bot.db');
    await db.initialize();

    const config = new Config();
    const summaryGenerator = new SummaryGenerator(db, mockTelegramBot, logger, config);

    // Add some test data
    const testMessages = [
        {
            whatsapp_id: 'summary_test_1',
            from_contact: '<EMAIL>',
            body: 'Hello, how are you?',
            timestamp: Date.now(),
            type: 'text',
            is_group: false,
            needs_attention: false
        },
        {
            whatsapp_id: 'summary_test_2',
            from_contact: '<EMAIL>',
            body: 'URGENT: Need help with the project!',
            timestamp: Date.now(),
            type: 'text',
            is_group: false,
            needs_attention: true,
            attention_reason: 'Contains urgent keywords: urgent'
        }
    ];

    for (const message of testMessages) {
        await db.saveMessage(message);
    }

    // Test summary generation (will use traditional format without AI)
    const today = new Date().toISOString().split('T')[0];
    const summary = await summaryGenerator.generateDailySummary(today);

    console.log('✅ Summary generated successfully');

    await db.close();
    console.log('✅ Summary Generator test completed');
}

async function testAIService() {
    console.log('\n🤖 Testing AI Service...');

    try {
        const config = new Config();
        const database = new Database('./data/test_whatsapp_bot.db');
        await database.initialize();

        const aiService = new OpenAIService(config, database, logger);

        console.log(`✅ AI Service initialized`);
        console.log(`   Enabled: ${aiService.isEnabled}`);
        console.log(`   API Key configured: ${!!config.openai?.apiKey}`);

        // Test usage stats
        const stats = await aiService.getUsageStats();
        console.log(`✅ Usage stats retrieved`);
        console.log(`   Requests today: ${stats.requestCount}`);
        console.log(`   Total cost: $${stats.totalCost.toFixed(4)}`);

        // Test privacy sanitization
        const testText = "My phone is ************ and <NAME_EMAIL>";
        const sanitized = aiService.sanitizeContent(testText);
        console.log(`✅ Privacy sanitization working`);
        console.log(`   Original: ${testText}`);
        console.log(`   Sanitized: ${sanitized}`);

        if (aiService.isEnabled && config.openai?.apiKey) {
            console.log('⚠️ AI service is enabled but skipping live API test to avoid costs');
            console.log('   Run "npm run ai test" to test with actual API calls');
        } else {
            console.log('ℹ️ AI service disabled or no API key - this is normal for testing');
        }

        await database.close();
        console.log('✅ AI Service test completed');

    } catch (error) {
        console.error('❌ AI Service test failed:', error.message);
    }
}

async function runAllTests() {
    console.log('🧪 Starting WhatsApp Bot Tests...\n');

    try {
        await testConfig();
        await testDatabase();
        await testMessageProcessor();
        await testResponseSystem();
        await testSummaryGenerator();
        await testAIService();

        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('1. Run "npm run setup" to configure your bot');
        console.log('2. Add your OpenAI API key to .env for AI features (optional)');
        console.log('3. Run "npm run dev" to start the bot');
        console.log('4. Scan the QR code with WhatsApp');
        console.log('5. Check Telegram for confirmation messages');
        console.log('\n🤖 AI Features:');
        console.log('• Run "npm run ai status" to check AI configuration');
        console.log('• Run "npm run ai test" to test AI functionality');
        console.log('• Use "/ai status" in Telegram to monitor AI usage');

    } catch (error) {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    }
}

// Handle errors
process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});

// Run tests
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testDatabase,
    testConfig,
    testMessageProcessor,
    testResponseSystem,
    testSummaryGenerator,
    testAIService
};
