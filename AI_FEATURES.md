# WhatsApp Intelligent Bot - AI Features Guide

## 🤖 Overview

Your WhatsApp bot now includes advanced AI-powered features using OpenAI's GPT models. These features enhance the bot's intelligence while maintaining full backward compatibility with the existing pattern-based system.

## 🚀 AI Features

### 1. **AI-Generated Response System**
- **Context-Aware Responses**: Analyzes full message context, sender profile, and conversation history
- **Relationship-Based Responses**: Adapts tone based on contact type (colleague, client, family)
- **Intelligent Fallback**: Falls back to pattern-based responses if AI fails
- **Multi-language Support**: Supports English and Italian with natural language generation

### 2. **AI-Enhanced Daily Summaries**
- **Natural Language Summaries**: Replaces bullet points with narrative summaries
- **Smart Categorization**: AI-powered topic and urgency analysis
- **Action Item Extraction**: Automatically identifies deadlines, meetings, and file requests
- **Sentiment Analysis**: Analyzes conversation sentiment and identifies concerning messages
- **Executive Insights**: Provides actionable recommendations

### 3. **AI-Powered Telegram Notifications**
- **Contextual Explanations**: AI explains why messages need attention
- **Suggested Responses**: Provides 2-3 response options for urgent messages
- **Smart Contact Management**: AI suggests contact importance based on patterns
- **Conversational Status Updates**: Human-readable status instead of technical data

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# AI Settings
AI_ENABLED=true
AI_MAX_TOKENS=150
AI_TEMPERATURE=0.7
AI_LANGUAGE=en
AI_PERSONALITY=professional
```

### User Configuration (config/user-config.json)
```json
{
  "ai": {
    "enabled": true,
    "features": {
      "smartResponses": true,
      "intelligentSummaries": true,
      "sentimentAnalysis": true,
      "contextualNotifications": true
    },
    "model": "gpt-3.5-turbo",
    "maxTokens": 150,
    "temperature": 0.7,
    "language": "en",
    "personality": "professional",
    "rateLimits": {
      "requestsPerDay": 1000,
      "maxCostPerHour": 5.0,
      "maxCostPerDay": 20.0
    },
    "fallbackToPatterns": true,
    "privacyMode": true
  }
}
```

## 🛠️ Management Commands

### Terminal Commands
```bash
# Check AI status
npm run ai status

# Toggle AI on/off
npm run ai toggle

# View costs and usage
npm run ai costs

# Configure AI settings
npm run ai config model gpt-4
npm run ai config language it
npm run ai config personality friendly
npm run ai config temperature 0.8
npm run ai config maxTokens 200

# Test AI functionality
npm run ai test
```

### Telegram Commands
```
/ai status    - Check AI service status and usage
/ai toggle    - Enable/disable AI features
/ai costs     - View detailed cost breakdown
```

## 💰 Cost Management

### Model Pricing (per 1K tokens)
- **gpt-4**: Input $0.03, Output $0.06 (Most capable, highest cost)
- **gpt-4-turbo**: Input $0.01, Output $0.03 (Fast and capable)
- **gpt-3.5-turbo**: Input $0.0015, Output $0.002 (Recommended balance)
- **gpt-4o-mini**: Input $0.00015, Output $0.0006 (Fastest, cheapest)

### Cost Control Features
- **Daily/Hourly Limits**: Configurable spending limits
- **Usage Monitoring**: Real-time cost tracking
- **Budget Alerts**: Notifications when approaching limits
- **Token Counting**: Accurate cost estimation
- **Rate Limiting**: Prevents excessive API usage

### Cost Optimization Tips
1. Use `gpt-3.5-turbo` for most tasks
2. Set lower `maxTokens` for responses
3. Reduce `temperature` for more focused responses
4. Enable `fallbackToPatterns` to reduce API calls
5. Disable unused AI features

## 🔒 Privacy & Security

### Privacy Protection
- **Data Sanitization**: Removes phone numbers, emails, and sensitive data before API calls
- **Local Processing**: All data stored locally, not shared externally
- **Configurable Privacy Mode**: Enhanced privacy filtering
- **No Training Data**: Your data is not used to train OpenAI models

### Security Features
- **API Key Protection**: Secure credential management
- **Error Handling**: Graceful degradation on API failures
- **Audit Logging**: Complete AI usage tracking
- **Access Control**: User-controlled feature toggles

## 📊 Monitoring & Analytics

### Usage Statistics
- **Request Count**: Daily/weekly API request tracking
- **Token Usage**: Input/output token consumption
- **Cost Tracking**: Real-time cost monitoring
- **Feature Usage**: Per-feature usage analytics
- **Performance Metrics**: Response times and success rates

### Available Reports
```bash
# Daily usage summary
npm run ai costs

# Feature-specific analytics
npm run ai status

# Performance monitoring
npm run health
```

## 🔧 Troubleshooting

### Common Issues

**AI Features Not Working**
```bash
# Check AI status
npm run ai status

# Verify API key
echo $OPENAI_API_KEY

# Test connectivity
npm run ai test
```

**High Costs**
```bash
# Check usage
npm run ai costs

# Reduce model complexity
npm run ai config model gpt-3.5-turbo

# Lower token limits
npm run ai config maxTokens 100
```

**API Rate Limits**
- Check OpenAI dashboard for account limits
- Reduce `requestsPerDay` in configuration
- Enable `fallbackToPatterns` for graceful degradation

### Error Messages
- **"OpenAI service not available"**: Check API key and internet connection
- **"Daily request limit exceeded"**: Increase limits or wait for reset
- **"AI response generation failed"**: Check API status and fallback settings

## 🌟 Best Practices

### Configuration Recommendations
1. **Start Conservative**: Begin with `gpt-3.5-turbo` and low limits
2. **Monitor Costs**: Check usage daily for the first week
3. **Enable Fallbacks**: Always keep pattern-based responses as backup
4. **Test Thoroughly**: Use `npm run ai test` before production

### Usage Optimization
1. **Smart Feature Selection**: Only enable features you need
2. **Language Consistency**: Set language to match your contacts
3. **Personality Matching**: Choose personality that fits your communication style
4. **Regular Monitoring**: Check `/ai status` in Telegram regularly

### Security Guidelines
1. **Protect API Keys**: Never commit API keys to version control
2. **Monitor Usage**: Watch for unexpected cost spikes
3. **Regular Updates**: Keep dependencies updated
4. **Backup Configurations**: Save working configurations

## 🔄 Hybrid System

The bot operates as a hybrid system:

1. **AI First**: Attempts AI-generated responses when enabled
2. **Pattern Fallback**: Falls back to pattern-based responses on failure
3. **Graceful Degradation**: Continues working even if AI is unavailable
4. **User Control**: Complete control over which features use AI

This ensures your bot remains reliable while providing enhanced intelligence when possible.

## 📈 Future Enhancements

Planned AI improvements:
- **Voice Message Processing**: AI analysis of voice messages
- **Image Recognition**: Analysis of shared images and documents
- **Advanced Learning**: Personalized response improvement over time
- **Multi-Model Support**: Integration with other AI providers
- **Custom Training**: Fine-tuning on your specific communication patterns

---

**Ready to use AI features?** Start with `npm run ai status` to check your configuration!
