/**
 * Configuration Management
 * Handles all bot configuration including user profiles, response patterns, and settings
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

class Config {
    constructor() {
        this.loadEnvironmentConfig();
        this.loadUserConfig();
    }

    loadEnvironmentConfig() {
        this.telegram = {
            botToken: process.env.TELEGRAM_BOT_TOKEN,
            chatId: process.env.TELEGRAM_CHAT_ID
        };

        this.database = {
            path: process.env.DB_PATH || './data/whatsapp_bot.db'
        };

        this.timezone = process.env.TIMEZONE || 'Europe/Rome';
        
        this.openai = {
            apiKey: process.env.OPENAI_API_KEY,
            model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo'
        };

        this.ai = {
            enabled: process.env.AI_ENABLED !== 'false',
            model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
            maxTokens: parseInt(process.env.AI_MAX_TOKENS) || 150,
            temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.7,
            language: process.env.AI_LANGUAGE || 'en',
            personality: process.env.AI_PERSONALITY || 'professional'
        };

        // Validate required environment variables
        if (!this.telegram.botToken || !this.telegram.chatId) {
            throw new Error('TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID are required in environment variables');
        }
    }

    loadUserConfig() {
        const configPath = path.join(__dirname, '../../config/user-config.json');
        
        try {
            if (fs.existsSync(configPath)) {
                const userConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                this.userProfiles = userConfig.userProfiles || {};
                this.responsePatterns = userConfig.responsePatterns || {};
                this.importantContacts = userConfig.importantContacts || [];
                this.urgencyKeywords = userConfig.urgencyKeywords || [];
                this.businessHours = userConfig.businessHours || {};
                this.doNotDisturbMode = userConfig.doNotDisturbMode || {};
            } else {
                this.setDefaultUserConfig();
                this.saveUserConfig();
            }
        } catch (error) {
            console.warn('Error loading user config, using defaults:', error.message);
            this.setDefaultUserConfig();
        }
    }

    setDefaultUserConfig() {
        this.userProfiles = {
            // Example profiles - will be populated based on actual contacts
            // "<EMAIL>": {
            //     name: "Marco",
            //     type: "colleague",
            //     autoRespond: true,
            //     customResponse: "I'm currently in a meeting. I'll get back to you soon.",
            //     priority: "normal"
            // }
        };

        this.responsePatterns = {
            greetings: {
                patterns: [
                    "^(hi|hello|hey|ciao|salve)",
                    "^good (morning|afternoon|evening)"
                ],
                responses: [
                    "Hi! I'm currently not available. I'll get back to you as soon as possible.",
                    "Hello! Thanks for your message. I'll respond shortly.",
                    "Hey! I'm away right now but will reply soon."
                ]
            },
            fileRequests: {
                patterns: [
                    "can you send (me )?the",
                    "please send",
                    "need the (file|document|report)",
                    "where is the"
                ],
                responses: [
                    "I've noted your request for the file. I'll send it to you shortly.",
                    "Thanks for the request. I'll get that file to you as soon as I can.",
                    "Got it! I'll send you the document you requested."
                ]
            },
            meetingQuestions: {
                patterns: [
                    "when (is|are) (we|the) meeting",
                    "what time (is )?the meeting",
                    "meeting time",
                    "when do we meet"
                ],
                responses: [
                    "Let me check the calendar and get back to you with the meeting details.",
                    "I'll confirm the meeting time and send you the details shortly.",
                    "Thanks for asking! I'll send you the meeting information in a moment."
                ]
            },
            availability: {
                patterns: [
                    "are you (free|available)",
                    "can you (talk|call)",
                    "do you have time"
                ],
                responses: [
                    "I'm not available right now, but I'll get back to you soon to discuss.",
                    "Currently busy, but I'll reach out to you shortly to chat.",
                    "Not available at the moment, but let's connect soon!"
                ]
            }
        };

        this.importantContacts = [
            // Will be populated with actual contact IDs
            // "<EMAIL>"
        ];

        this.urgencyKeywords = [
            'urgent', 'emergency', 'asap', 'immediately', 'critical', 'problem', 
            'issue', 'blocked', 'help', 'stuck', 'deadline', 'today', 'now',
            'urgente', 'emergenza', 'subito', 'problema', 'aiuto', 'bloccato'
        ];

        this.businessHours = {
            enabled: true,
            timezone: 'Europe/Rome',
            schedule: {
                monday: { start: '09:00', end: '18:00' },
                tuesday: { start: '09:00', end: '18:00' },
                wednesday: { start: '09:00', end: '18:00' },
                thursday: { start: '09:00', end: '18:00' },
                friday: { start: '09:00', end: '18:00' },
                saturday: null, // No business hours
                sunday: null    // No business hours
            },
            outOfHoursResponse: "Thanks for your message! I'm currently outside business hours (9 AM - 6 PM, Mon-Fri). I'll get back to you during business hours."
        };

        this.doNotDisturbMode = {
            enabled: false,
            schedule: {
                start: '22:00',
                end: '08:00'
            },
            autoResponse: "I'm currently in Do Not Disturb mode. I'll respond to your message tomorrow morning.",
            allowUrgent: true,
            allowImportantContacts: true
        };

        this.ai = {
            enabled: true,
            features: {
                smartResponses: true,
                intelligentSummaries: true,
                sentimentAnalysis: true,
                contextualNotifications: true
            },
            model: 'gpt-3.5-turbo',
            maxTokens: 150,
            temperature: 0.7,
            language: 'en', // 'en' or 'it'
            personality: 'professional', // 'professional', 'friendly', 'formal', 'casual'
            rateLimits: {
                requestsPerDay: 1000,
                maxCostPerHour: 5.0,
                maxCostPerDay: 20.0
            },
            fallbackToPatterns: true,
            privacyMode: true
        };
    }

    saveUserConfig() {
        const configPath = path.join(__dirname, '../../config/user-config.json');
        const configDir = path.dirname(configPath);
        
        // Ensure config directory exists
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }

        const userConfig = {
            userProfiles: this.userProfiles,
            responsePatterns: this.responsePatterns,
            importantContacts: this.importantContacts,
            urgencyKeywords: this.urgencyKeywords,
            businessHours: this.businessHours,
            doNotDisturbMode: this.doNotDisturbMode
        };

        fs.writeFileSync(configPath, JSON.stringify(userConfig, null, 2));
    }

    // Helper methods
    getUserProfile(contactId) {
        return this.userProfiles[contactId] || null;
    }

    addUserProfile(contactId, profile) {
        this.userProfiles[contactId] = profile;
        this.saveUserConfig();
    }

    updateUserProfile(contactId, updates) {
        if (this.userProfiles[contactId]) {
            this.userProfiles[contactId] = { ...this.userProfiles[contactId], ...updates };
            this.saveUserConfig();
        }
    }

    isImportantContact(contactId) {
        return this.importantContacts.includes(contactId);
    }

    addImportantContact(contactId) {
        if (!this.importantContacts.includes(contactId)) {
            this.importantContacts.push(contactId);
            this.saveUserConfig();
        }
    }

    removeImportantContact(contactId) {
        const index = this.importantContacts.indexOf(contactId);
        if (index > -1) {
            this.importantContacts.splice(index, 1);
            this.saveUserConfig();
        }
    }

    isBusinessHours() {
        if (!this.businessHours.enabled) return true;

        const now = new Date();
        const dayName = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

        const daySchedule = this.businessHours.schedule[dayName];
        if (!daySchedule) return false; // No business hours for this day

        return currentTime >= daySchedule.start && currentTime <= daySchedule.end;
    }

    isDoNotDisturbTime() {
        if (!this.doNotDisturbMode.enabled) return false;

        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

        const { start, end } = this.doNotDisturbMode.schedule;
        
        // Handle overnight DND (e.g., 22:00 to 08:00)
        if (start > end) {
            return currentTime >= start || currentTime <= end;
        } else {
            return currentTime >= start && currentTime <= end;
        }
    }

    getRandomResponse(patternKey) {
        const pattern = this.responsePatterns[patternKey];
        if (!pattern || !pattern.responses.length) return null;
        
        const randomIndex = Math.floor(Math.random() * pattern.responses.length);
        return pattern.responses[randomIndex];
    }
}

module.exports = Config;
