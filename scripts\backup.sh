#!/bin/bash

# WhatsApp Bot Backup Script
# Creates backups of database, configuration, and session data

set -e

# Configuration
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "🔄 Starting WhatsApp Bot backup..."
echo "📁 Bot directory: $BOT_DIR"
echo "📅 Backup timestamp: $DATE"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create timestamped backup folder
BACKUP_PATH="$BACKUP_DIR/backup_$DATE"
mkdir -p "$BACKUP_PATH"

echo "📦 Creating backup at: $BACKUP_PATH"

# Backup database
if [ -f "$BOT_DIR/data/whatsapp_bot.db" ]; then
    echo "💾 Backing up database..."
    cp "$BOT_DIR/data/whatsapp_bot.db" "$BACKUP_PATH/whatsapp_bot.db"
    echo "✅ Database backed up"
else
    echo "⚠️ Database not found, skipping..."
fi

# Backup configuration
if [ -d "$BOT_DIR/config" ]; then
    echo "⚙️ Backing up configuration..."
    cp -r "$BOT_DIR/config" "$BACKUP_PATH/"
    echo "✅ Configuration backed up"
else
    echo "⚠️ Configuration directory not found, skipping..."
fi

# Backup environment file (without sensitive data)
if [ -f "$BOT_DIR/.env" ]; then
    echo "🔐 Backing up environment template..."
    # Create sanitized version of .env
    grep -v "TOKEN\|KEY\|SECRET" "$BOT_DIR/.env" > "$BACKUP_PATH/.env.template" || true
    echo "✅ Environment template backed up"
fi

# Backup sessions (optional - contains authentication data)
if [ -d "$BOT_DIR/sessions" ] && [ "$(ls -A $BOT_DIR/sessions)" ]; then
    read -p "🔑 Backup WhatsApp sessions? (contains auth data) [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔑 Backing up sessions..."
        cp -r "$BOT_DIR/sessions" "$BACKUP_PATH/"
        echo "✅ Sessions backed up"
    else
        echo "⏭️ Sessions backup skipped"
    fi
fi

# Create backup info file
cat > "$BACKUP_PATH/backup_info.txt" << EOF
WhatsApp Bot Backup Information
===============================

Backup Date: $(date)
Bot Version: $(node -p "require('$BOT_DIR/package.json').version" 2>/dev/null || echo "Unknown")
Node Version: $(node --version)
Platform: $(uname -a)

Contents:
- Database: $([ -f "$BACKUP_PATH/whatsapp_bot.db" ] && echo "✅ Included" || echo "❌ Not found")
- Configuration: $([ -d "$BACKUP_PATH/config" ] && echo "✅ Included" || echo "❌ Not found")
- Environment Template: $([ -f "$BACKUP_PATH/.env.template" ] && echo "✅ Included" || echo "❌ Not found")
- Sessions: $([ -d "$BACKUP_PATH/sessions" ] && echo "✅ Included" || echo "❌ Not included")

Restore Instructions:
1. Stop the bot: pm2 stop whatsapp-bot (or Ctrl+C if running in dev)
2. Restore database: cp whatsapp_bot.db ../data/
3. Restore config: cp -r config/* ../config/
4. Restore sessions: cp -r sessions/* ../sessions/ (if backed up)
5. Start the bot: npm run dev or pm2 start whatsapp-bot

EOF

# Compress backup
echo "🗜️ Compressing backup..."
cd "$BACKUP_DIR"
tar -czf "backup_$DATE.tar.gz" "backup_$DATE"
rm -rf "backup_$DATE"

echo "✅ Backup completed successfully!"
echo "📦 Backup file: $BACKUP_DIR/backup_$DATE.tar.gz"

# Cleanup old backups (keep last 7)
echo "🧹 Cleaning up old backups..."
ls -t backup_*.tar.gz 2>/dev/null | tail -n +8 | xargs -r rm --
echo "✅ Cleanup completed"

# Show backup size
BACKUP_SIZE=$(du -h "backup_$DATE.tar.gz" | cut -f1)
echo "📊 Backup size: $BACKUP_SIZE"

echo "🎉 Backup process completed successfully!"
echo ""
echo "To restore from this backup:"
echo "  tar -xzf backup_$DATE.tar.gz"
echo "  cd backup_$DATE"
echo "  # Follow instructions in backup_info.txt"
