#!/usr/bin/env node

/**
 * Reset Telegram Webhook
 * Fixes the 409 Conflict error by deleting any existing webhook
 */

require('dotenv').config();
const https = require('https');

const token = process.env.TELEGRAM_BOT_TOKEN;

if (!token) {
    console.error('❌ TELEGRAM_BOT_TOKEN must be set in .env');
    process.exit(1);
}

console.log('🔧 Resetting Telegram webhook...');

// Function to make HTTPS request
function makeRequest(url) {
    return new Promise((resolve, reject) => {
        https.get(url, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (error) {
                    reject(error);
                }
            });
        }).on('error', (error) => {
            reject(error);
        });
    });
}

async function resetWebhook() {
    try {
        // First, check current webhook info
        console.log('📋 Checking current webhook info...');
        const webhookInfo = await makeRequest(`https://api.telegram.org/bot${token}/getWebhookInfo`);
        
        if (webhookInfo.ok) {
            console.log('Current webhook:', webhookInfo.result);
            
            if (webhookInfo.result.url) {
                console.log(`🔗 Webhook URL found: ${webhookInfo.result.url}`);
            } else {
                console.log('✅ No webhook URL set');
            }
        }

        // Delete webhook to enable polling
        console.log('🗑️ Deleting webhook...');
        const deleteResult = await makeRequest(`https://api.telegram.org/bot${token}/deleteWebhook`);
        
        if (deleteResult.ok) {
            console.log('✅ Webhook deleted successfully');
        } else {
            console.log('❌ Failed to delete webhook:', deleteResult);
        }

        // Get pending updates to clear them
        console.log('📥 Getting pending updates...');
        const updatesResult = await makeRequest(`https://api.telegram.org/bot${token}/getUpdates?offset=-1&limit=1`);
        
        if (updatesResult.ok) {
            console.log(`📊 Pending updates: ${updatesResult.result.length}`);
            
            if (updatesResult.result.length > 0) {
                const lastUpdateId = updatesResult.result[updatesResult.result.length - 1].update_id;
                console.log(`🔄 Clearing updates up to ID: ${lastUpdateId}`);
                
                // Clear all pending updates
                const clearResult = await makeRequest(`https://api.telegram.org/bot${token}/getUpdates?offset=${lastUpdateId + 1}&limit=1`);
                
                if (clearResult.ok) {
                    console.log('✅ Pending updates cleared');
                } else {
                    console.log('❌ Failed to clear updates:', clearResult);
                }
            }
        } else {
            console.log('❌ Failed to get updates:', updatesResult);
        }

        // Verify webhook is deleted
        console.log('🔍 Verifying webhook deletion...');
        const verifyResult = await makeRequest(`https://api.telegram.org/bot${token}/getWebhookInfo`);
        
        if (verifyResult.ok) {
            if (!verifyResult.result.url) {
                console.log('✅ Webhook successfully removed - polling is now enabled');
            } else {
                console.log('⚠️ Webhook still exists:', verifyResult.result.url);
            }
        }

        console.log('\n🎉 Telegram webhook reset complete!');
        console.log('You can now start your bot with polling enabled.');
        
    } catch (error) {
        console.error('❌ Error resetting webhook:', error);
        process.exit(1);
    }
}

resetWebhook();
