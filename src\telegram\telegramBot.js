/**
 * Telegram Bot Integration
 * Handles notifications and command interface via Telegram
 */

const TelegramBot = require('node-telegram-bot-api');

const OpenAIService = require('../ai/openaiService');

class TelegramBotManager {
    constructor(token, chatId, config = null, database = null, logger = null) {
        this.token = token;
        this.chatId = chatId;
        this.config = config;
        this.database = database;
        this.logger = logger;
        this.bot = null;
        this.isInitialized = false;
        this.aiService = config && database && logger ? new OpenAIService(config, database, logger) : null;
    }

    async initialize() {
        try {
            this.bot = new TelegramBot(this.token, { polling: true });
            
            // Set up command handlers
            this.setupCommandHandlers();
            
            // Test connection
            const me = await this.bot.getMe();
            console.log(`Telegram bot initialized: @${me.username}`);
            
            this.isInitialized = true;
            
            // Send startup message
            await this.sendMessage('🤖 WhatsApp Bot is starting up...');
            
        } catch (error) {
            console.error('Failed to initialize Telegram bot:', error);
            throw error;
        }
    }

    setupCommandHandlers() {
        // Start command
        this.bot.onText(/\/start/, async (msg) => {
            const chatId = msg.chat.id;
            const welcomeMessage = `
🤖 *WhatsApp Bot Control Panel*

Available commands:
/status - Check bot status
/summary - Get today's summary
/contacts - List important contacts
/settings - View current settings
/dnd - Toggle Do Not Disturb mode
/help - Show this help message

The bot will automatically notify you about:
• Messages requiring attention
• Daily summaries at 8 PM
• System status updates
            `;
            
            await this.bot.sendMessage(chatId, welcomeMessage, { parse_mode: 'Markdown' });
        });

        // Status command
        this.bot.onText(/\/status/, async (msg) => {
            const chatId = msg.chat.id;
            try {
                const status = await this.getSystemStatus();
                const statusMessage = `
📊 *Bot Status*

${status.whatsapp ? '🟢' : '🔴'} WhatsApp: ${status.whatsapp ? 'Connected' : 'Disconnected'}
🟢 Telegram: Active
${status.database ? '🟢' : '🔴'} Database: ${status.database ? 'Online' : 'Offline'}

📈 *Today's Stats*
• Messages processed: ${status.todayMessages || 0}
• Auto responses sent: ${status.autoResponses || 0}
• Attention alerts: ${status.attentionAlerts || 0}
• Unique contacts: ${status.uniqueContacts || 0}

⚙️ *Settings*
• Auto-response: ${status.autoResponseEnabled ? '🟢 Enabled' : '🔴 Disabled'}
• Business hours: ${status.businessHoursActive ? '🟢 Active' : '🔴 Inactive'}
• DND mode: ${status.dndMode ? '🟡 Active' : '🟢 Inactive'}
                `;

                await this.bot.sendMessage(chatId, statusMessage, { parse_mode: 'Markdown' });
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error getting status: ' + error.message);
            }
        });

        // Summary command
        this.bot.onText(/\/summary/, async (msg) => {
            const chatId = msg.chat.id;
            await this.bot.sendMessage(chatId, '📝 Generating current summary...', { parse_mode: 'Markdown' });
            // Trigger summary generation through callback if available
            if (this.onSummaryRequest) {
                await this.onSummaryRequest();
            }
        });

        // DND command
        this.bot.onText(/\/dnd/, async (msg) => {
            const chatId = msg.chat.id;
            try {
                const currentStatus = await this.getDNDStatus();
                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: currentStatus ? '🔴 Disable DND' : '🟡 Enable DND', callback_data: 'toggle_dnd' }
                        ],
                        [
                            { text: '⚙️ DND Settings', callback_data: 'dnd_settings' }
                        ]
                    ]
                };

                const message = `
🌙 *Do Not Disturb Mode*

Status: ${currentStatus ? '🟡 Active' : '🟢 Inactive'}

${currentStatus ? 'DND mode is currently active. Only urgent messages and important contacts will trigger notifications.' : 'DND mode is currently inactive. All messages will be processed normally.'}
                `;

                await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error managing DND mode: ' + error.message);
            }
        });

        // Contacts command
        this.bot.onText(/\/contacts/, async (msg) => {
            const chatId = msg.chat.id;
            try {
                const contacts = await this.getImportantContacts();
                let message = '👥 *Important Contacts*\n\n';

                if (contacts.length === 0) {
                    message += 'No important contacts configured yet.\n\n';
                } else {
                    contacts.forEach((contact, index) => {
                        message += `${index + 1}. ${contact.name || contact.whatsapp_id}\n`;
                        message += `   📱 ${contact.whatsapp_id}\n`;
                        message += `   📊 ${contact.message_count || 0} messages\n\n`;
                    });
                }

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '➕ Add Contact', callback_data: 'add_contact' },
                            { text: '📊 Contact Stats', callback_data: 'contact_stats' }
                        ]
                    ]
                };

                await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error getting contacts: ' + error.message);
            }
        });

        // AI commands
        this.bot.onText(/\/ai (.+)/, async (msg, match) => {
            const chatId = msg.chat.id;
            const command = match[1].toLowerCase();

            try {
                switch (command) {
                    case 'status':
                        await this.handleAIStatus(chatId);
                        break;
                    case 'toggle':
                        await this.handleAIToggle(chatId);
                        break;
                    case 'costs':
                        await this.handleAICosts(chatId);
                        break;
                    default:
                        await this.bot.sendMessage(chatId, `
🤖 *AI Commands*

Available commands:
/ai status - Check AI service status
/ai toggle - Enable/disable AI features
/ai costs - View usage and costs

Example: \`/ai status\`
                        `, { parse_mode: 'Markdown' });
                }
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error executing AI command: ' + error.message);
            }
        });

        // Help command
        this.bot.onText(/\/help/, async (msg) => {
            const chatId = msg.chat.id;
            const helpMessage = `
🆘 *WhatsApp Bot Help*

*Automatic Features:*
• AI-powered smart responses (when enabled)
• Intelligent daily summaries with insights
• Advanced message analysis and sentiment detection
• Context-aware notifications
• Fallback to pattern-based responses

*Commands:*
/status - Bot and system status
/summary - Generate current summary
/contacts - Manage important contacts
/dnd - Toggle Do Not Disturb mode
/ai status - AI service status
/ai toggle - Enable/disable AI features
/ai costs - View AI usage and costs

*AI Features:*
• Context-aware response generation
• Sentiment analysis
• Intelligent summary generation
• Smart contact management suggestions
• Multi-language support (English/Italian)

*Message Types Detected:*
• Greetings and casual messages
• File and document requests
• Meeting and scheduling questions
• Availability inquiries
• Urgent messages with context analysis
• Action items and deadlines

*Notifications:*
You'll receive intelligent alerts with context explanations and suggested response options.
            `;

            await this.bot.sendMessage(chatId, helpMessage, { parse_mode: 'Markdown' });
        });

        // Handle callback queries (inline keyboard responses)
        this.bot.on('callback_query', async (callbackQuery) => {
            const action = callbackQuery.data;
            const msg = callbackQuery.message;
            
            try {
                await this.handleCallbackQuery(action, msg, callbackQuery);
            } catch (error) {
                console.error('Error handling callback query:', error);
            }
        });

        // Handle errors
        this.bot.on('error', (error) => {
            console.error('Telegram bot error:', error);
        });

        this.bot.on('polling_error', (error) => {
            console.error('Telegram polling error:', error);
        });
    }

    async handleCallbackQuery(action, msg, callbackQuery) {
        const chatId = msg.chat.id;
        const messageId = msg.message_id;

        try {
            switch (action) {
                case 'toggle_dnd':
                    if (this.onToggleDND) {
                        const newStatus = await this.onToggleDND();
                        await this.bot.editMessageText(
                            `🌙 DND mode ${newStatus ? 'enabled' : 'disabled'}.`,
                            { chat_id: chatId, message_id: messageId }
                        );
                    }
                    break;

                case 'dnd_settings':
                    await this.bot.editMessageText(
                        `⚙️ *DND Settings*\n\nUse the configuration file to customize DND settings:\n• Schedule (start/end times)\n• Allow urgent messages\n• Allow important contacts`,
                        { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' }
                    );
                    break;

                case 'add_contact':
                    await this.bot.editMessageText(
                        `➕ *Add Important Contact*\n\nTo add a contact as important:\n1. Wait for a message from them\n2. I'll ask if you want to mark them as important\n3. Or manually edit the config file`,
                        { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' }
                    );
                    break;

                case 'contact_stats':
                    if (this.onContactStats) {
                        const stats = await this.onContactStats();
                        await this.bot.editMessageText(
                            `📊 *Contact Statistics*\n\n${stats}`,
                            { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' }
                        );
                    }
                    break;

                default:
                    if (action.startsWith('respond_')) {
                        const contactId = action.replace('respond_', '');
                        await this.bot.editMessageText(
                            `✅ You chose to respond to this message manually.`,
                            { chat_id: chatId, message_id: messageId }
                        );
                    } else if (action.startsWith('auto_handle_')) {
                        const contactId = action.replace('auto_handle_', '');
                        await this.bot.editMessageText(
                            `🤖 Message will be handled automatically.`,
                            { chat_id: chatId, message_id: messageId }
                        );
                    }
                    break;
            }
        } catch (error) {
            console.error('Error handling callback query:', error);
            await this.bot.editMessageText(
                `❌ Error: ${error.message}`,
                { chat_id: chatId, message_id: messageId }
            );
        }

        // Answer the callback query to remove loading state
        await this.bot.answerCallbackQuery(callbackQuery.id);
    }

    async sendMessage(text, options = {}) {
        if (!this.isInitialized) {
            console.warn('Telegram bot not initialized, cannot send message');
            return;
        }

        try {
            return await this.bot.sendMessage(this.chatId, text, options);
        } catch (error) {
            console.error('Error sending Telegram message:', error);
        }
    }

    async sendAttentionAlert(messageData, reason) {
        const contactName = messageData.contact_name || messageData.from_contact;
        const messagePreview = messageData.body.length > 100
            ? messageData.body.substring(0, 100) + '...'
            : messageData.body;

        let alertText = `
⚠️ *Message Needs Attention*

👤 *From:* ${contactName}
📝 *Message:* ${messagePreview}
🔍 *Reason:* ${reason}
⏰ *Time:* ${new Date(messageData.timestamp).toLocaleString()}
        `;

        // Try to generate AI-enhanced explanation and suggestions
        if (this.aiService?.isEnabled && this.config?.ai?.features?.contextualNotifications) {
            try {
                const aiAnalysis = await this.generateAINotificationAnalysis(messageData, reason);
                if (aiAnalysis) {
                    alertText += `\n🤖 *AI Analysis:* ${aiAnalysis.explanation}`;

                    if (aiAnalysis.suggestions && aiAnalysis.suggestions.length > 0) {
                        alertText += `\n\n💡 *Suggested Responses:*`;
                        aiAnalysis.suggestions.forEach((suggestion, index) => {
                            alertText += `\n${index + 1}. ${suggestion}`;
                        });
                    }
                }
            } catch (error) {
                this.logger?.warn('AI notification analysis failed:', error.message);
            }
        }

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '✍️ I\'ll Respond', callback_data: `respond_${messageData.from_contact}` },
                    { text: '🤖 Auto Handle', callback_data: `auto_handle_${messageData.from_contact}` }
                ]
            ]
        };

        await this.sendMessage(alertText, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });
    }

    async generateAINotificationAnalysis(messageData, reason) {
        try {
            const prompt = `Analyze this WhatsApp message that needs attention and provide:
1. A brief explanation of why it needs attention
2. 2-3 suggested response options

Message: "${messageData.body}"
From: ${messageData.contact_name || 'Unknown contact'}
Reason flagged: ${reason}

Respond in JSON format:
{
  "explanation": "brief explanation",
  "suggestions": ["response 1", "response 2", "response 3"]
}`;

            const response = await this.aiService.generateResponse(prompt, {
                maxTokens: 200,
                temperature: 0.3
            });

            try {
                return JSON.parse(response.content);
            } catch (parseError) {
                // If JSON parsing fails, extract text manually
                return {
                    explanation: response.content.substring(0, 100),
                    suggestions: []
                };
            }
        } catch (error) {
            this.logger?.error('AI notification analysis failed:', error);
            return null;
        }
    }

    async sendDailySummary(summaryData) {
        let summaryText;

        if (summaryData.isAIGenerated && summaryData.aiSummary) {
            // AI-generated summary
            summaryText = `
📝 *AI-Generated Daily Summary*
📅 ${new Date().toLocaleDateString()}
🤖 *Generated by:* ${summaryData.aiSummary.model}

${summaryData.aiSummary.content}

📊 *Quick Stats:*
• Total messages: ${summaryData.totalMessages}
• Unique contacts: ${summaryData.uniqueContacts}
• Auto responses: ${summaryData.autoResponses}
• Urgent messages: ${summaryData.urgentMessages}

${summaryData.aiSummary.sentimentBreakdown ? `
😊 *Sentiment Analysis:*
• Positive: ${summaryData.aiSummary.sentimentBreakdown.positive.count} (${summaryData.aiSummary.sentimentBreakdown.positive.percentage}%)
• Negative: ${summaryData.aiSummary.sentimentBreakdown.negative.count} (${summaryData.aiSummary.sentimentBreakdown.negative.percentage}%)
• Neutral: ${summaryData.aiSummary.sentimentBreakdown.neutral.count} (${summaryData.aiSummary.sentimentBreakdown.neutral.percentage}%)
` : ''}

${summaryData.aiSummary.insights?.actionItems?.length > 0 ? `
📋 *Action Items Detected:*
${summaryData.aiSummary.insights.actionItems.slice(0, 3).map(item => `• ${item.contact}: ${item.message.substring(0, 50)}...`).join('\n')}
` : ''}

💰 *AI Cost:* $${summaryData.aiSummary.cost?.toFixed(4) || '0.0000'}
            `;
        } else {
            // Traditional summary
            summaryText = `
📝 *Daily WhatsApp Summary*
📅 ${new Date().toLocaleDateString()}

📊 *Statistics:*
• Total messages: ${summaryData.totalMessages}
• Unique contacts: ${summaryData.uniqueContacts}
• Auto responses: ${summaryData.autoResponses}
• Urgent messages: ${summaryData.urgentMessages}

${summaryData.contactsSummary ? `👥 *Active Contacts:*\n${summaryData.contactsSummary}\n` : ''}

${summaryData.requestsSummary ? `📋 *Requests & Follow-ups:*\n${summaryData.requestsSummary}\n` : ''}

${summaryData.urgentSummary ? `🚨 *Urgent Messages:*\n${summaryData.urgentSummary}\n` : ''}

${summaryData.unhandledSummary ? `⏳ *Unhandled Items:*\n${summaryData.unhandledSummary}` : ''}
            `;
        }

        await this.sendMessage(summaryText, { parse_mode: 'Markdown' });
    }

    async handleAIStatus(chatId) {
        try {
            if (!this.aiService) {
                await this.bot.sendMessage(chatId, '❌ AI service not available');
                return;
            }

            const stats = await this.aiService.getUsageStats();
            const statusMessage = `
🤖 *AI Service Status*

Status: ${stats.isEnabled ? '🟢 Enabled' : '🔴 Disabled'}

📊 *Today's Usage:*
• Requests: ${stats.requestCount}
• Total tokens: ${stats.totalTokens}
• Cost: $${stats.totalCost.toFixed(4)}

📈 *Limits:*
• Daily requests remaining: ${stats.dailyLimitRemaining}
• Daily cost remaining: $${stats.costLimitRemaining.toFixed(2)}

⚙️ *Features:*
• Smart responses: ${this.config?.ai?.features?.smartResponses ? '✅' : '❌'}
• Intelligent summaries: ${this.config?.ai?.features?.intelligentSummaries ? '✅' : '❌'}
• Sentiment analysis: ${this.config?.ai?.features?.sentimentAnalysis ? '✅' : '❌'}
• Contextual notifications: ${this.config?.ai?.features?.contextualNotifications ? '✅' : '❌'}

🌐 *Settings:*
• Model: ${this.config?.ai?.model || 'gpt-3.5-turbo'}
• Language: ${this.config?.ai?.language || 'en'}
• Personality: ${this.config?.ai?.personality || 'professional'}
            `;

            await this.bot.sendMessage(chatId, statusMessage, { parse_mode: 'Markdown' });
        } catch (error) {
            await this.bot.sendMessage(chatId, '❌ Error getting AI status: ' + error.message);
        }
    }

    async handleAIToggle(chatId) {
        try {
            if (!this.aiService) {
                await this.bot.sendMessage(chatId, '❌ AI service not available');
                return;
            }

            const newStatus = await this.aiService.toggleService(!this.aiService.isEnabled);
            const message = `🤖 AI service ${newStatus ? 'enabled' : 'disabled'}`;

            await this.bot.sendMessage(chatId, message);
        } catch (error) {
            await this.bot.sendMessage(chatId, '❌ Error toggling AI service: ' + error.message);
        }
    }

    async handleAICosts(chatId) {
        try {
            if (!this.aiService) {
                await this.bot.sendMessage(chatId, '❌ AI service not available');
                return;
            }

            const stats = await this.aiService.getUsageStats();
            const costMessage = `
💰 *AI Usage & Costs*

📅 *Today (${stats.lastReset}):*
• Requests: ${stats.requestCount}
• Total tokens: ${stats.totalTokens}
• Cost: $${stats.totalCost.toFixed(4)}

📊 *Breakdown:*
• Average cost per request: $${stats.requestCount > 0 ? (stats.totalCost / stats.requestCount).toFixed(4) : '0.0000'}
• Tokens per request: ${stats.requestCount > 0 ? Math.round(stats.totalTokens / stats.requestCount) : 0}

⚠️ *Limits:*
• Daily request limit: ${this.config?.ai?.rateLimits?.requestsPerDay || 1000}
• Daily cost limit: $${this.config?.ai?.rateLimits?.maxCostPerDay || 20.0}
• Hourly cost limit: $${this.config?.ai?.rateLimits?.maxCostPerHour || 5.0}

💡 *Tips to reduce costs:*
• Use shorter messages
• Disable features you don't need
• Lower the temperature setting
• Use gpt-3.5-turbo instead of gpt-4
            `;

            await this.bot.sendMessage(chatId, costMessage, { parse_mode: 'Markdown' });
        } catch (error) {
            await this.bot.sendMessage(chatId, '❌ Error getting cost information: ' + error.message);
        }
    }

    async sendSystemAlert(message, type = 'info') {
        const emoji = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅'
        };

        const alertText = `${emoji[type] || 'ℹ️'} *System Alert*\n\n${message}`;
        
        await this.sendMessage(alertText, { parse_mode: 'Markdown' });
    }

    async sendStatusUpdate(status) {
        const statusText = `
📊 *Bot Status Update*

🔄 *WhatsApp:* ${status.whatsapp ? '🟢 Connected' : '🔴 Disconnected'}
🔄 *Database:* ${status.database ? '🟢 Online' : '🔴 Offline'}
🔄 *Auto Response:* ${status.autoResponse ? '🟢 Active' : '🟡 Paused'}

📈 *Recent Activity:*
• Messages in last hour: ${status.recentMessages || 0}
• Auto responses sent: ${status.autoResponsesSent || 0}
        `;

        await this.sendMessage(statusText, { parse_mode: 'Markdown' });
    }

    async sendContactUpdate(contactData) {
        const updateText = `
👤 *Contact Update*

*Name:* ${contactData.name}
*Type:* ${contactData.type}
*Status:* ${contactData.status}

${contactData.message ? `*Message:* ${contactData.message}` : ''}
        `;

        await this.sendMessage(updateText, { parse_mode: 'Markdown' });
    }

    // Callback setters for integration with main bot
    setCallbacks(callbacks) {
        this.onSummaryRequest = callbacks.onSummaryRequest;
        this.onToggleDND = callbacks.onToggleDND;
        this.onContactStats = callbacks.onContactStats;
        this.getSystemStatus = callbacks.getSystemStatus;
        this.getDNDStatus = callbacks.getDNDStatus;
        this.getImportantContacts = callbacks.getImportantContacts;
    }

    // Default implementations (can be overridden)
    async getSystemStatus() {
        return {
            whatsapp: true,
            database: true,
            todayMessages: 0,
            autoResponses: 0,
            attentionAlerts: 0,
            uniqueContacts: 0,
            autoResponseEnabled: true,
            businessHoursActive: false,
            dndMode: false
        };
    }

    async getDNDStatus() {
        return false; // Default: DND is off
    }

    async getImportantContacts() {
        return []; // Default: no important contacts
    }

    stop() {
        if (this.bot) {
            this.bot.stopPolling();
            console.log('Telegram bot stopped');
        }
    }
}

module.exports = TelegramBotManager;
