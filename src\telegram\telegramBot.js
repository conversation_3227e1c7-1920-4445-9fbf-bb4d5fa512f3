/**
 * Telegram Bot Integration
 * Handles notifications and command interface via Telegram
 */

const TelegramBot = require('node-telegram-bot-api');

class TelegramBotManager {
    constructor(token, chatId) {
        this.token = token;
        this.chatId = chatId;
        this.bot = null;
        this.isInitialized = false;
    }

    async initialize() {
        try {
            this.bot = new TelegramBot(this.token, { polling: true });
            
            // Set up command handlers
            this.setupCommandHandlers();
            
            // Test connection
            const me = await this.bot.getMe();
            console.log(`Telegram bot initialized: @${me.username}`);
            
            this.isInitialized = true;
            
            // Send startup message
            await this.sendMessage('🤖 WhatsApp Bot is starting up...');
            
        } catch (error) {
            console.error('Failed to initialize Telegram bot:', error);
            throw error;
        }
    }

    setupCommandHandlers() {
        // Start command
        this.bot.onText(/\/start/, async (msg) => {
            const chatId = msg.chat.id;
            const welcomeMessage = `
🤖 *WhatsApp Bot Control Panel*

Available commands:
/status - Check bot status
/summary - Get today's summary
/contacts - List important contacts
/settings - View current settings
/dnd - Toggle Do Not Disturb mode
/help - Show this help message

The bot will automatically notify you about:
• Messages requiring attention
• Daily summaries at 8 PM
• System status updates
            `;
            
            await this.bot.sendMessage(chatId, welcomeMessage, { parse_mode: 'Markdown' });
        });

        // Status command
        this.bot.onText(/\/status/, async (msg) => {
            const chatId = msg.chat.id;
            try {
                const status = await this.getSystemStatus();
                const statusMessage = `
📊 *Bot Status*

${status.whatsapp ? '🟢' : '🔴'} WhatsApp: ${status.whatsapp ? 'Connected' : 'Disconnected'}
🟢 Telegram: Active
${status.database ? '🟢' : '🔴'} Database: ${status.database ? 'Online' : 'Offline'}

📈 *Today's Stats*
• Messages processed: ${status.todayMessages || 0}
• Auto responses sent: ${status.autoResponses || 0}
• Attention alerts: ${status.attentionAlerts || 0}
• Unique contacts: ${status.uniqueContacts || 0}

⚙️ *Settings*
• Auto-response: ${status.autoResponseEnabled ? '🟢 Enabled' : '🔴 Disabled'}
• Business hours: ${status.businessHoursActive ? '🟢 Active' : '🔴 Inactive'}
• DND mode: ${status.dndMode ? '🟡 Active' : '🟢 Inactive'}
                `;

                await this.bot.sendMessage(chatId, statusMessage, { parse_mode: 'Markdown' });
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error getting status: ' + error.message);
            }
        });

        // Summary command
        this.bot.onText(/\/summary/, async (msg) => {
            const chatId = msg.chat.id;
            await this.bot.sendMessage(chatId, '📝 Generating current summary...', { parse_mode: 'Markdown' });
            // Trigger summary generation through callback if available
            if (this.onSummaryRequest) {
                await this.onSummaryRequest();
            }
        });

        // DND command
        this.bot.onText(/\/dnd/, async (msg) => {
            const chatId = msg.chat.id;
            try {
                const currentStatus = await this.getDNDStatus();
                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: currentStatus ? '🔴 Disable DND' : '🟡 Enable DND', callback_data: 'toggle_dnd' }
                        ],
                        [
                            { text: '⚙️ DND Settings', callback_data: 'dnd_settings' }
                        ]
                    ]
                };

                const message = `
🌙 *Do Not Disturb Mode*

Status: ${currentStatus ? '🟡 Active' : '🟢 Inactive'}

${currentStatus ? 'DND mode is currently active. Only urgent messages and important contacts will trigger notifications.' : 'DND mode is currently inactive. All messages will be processed normally.'}
                `;

                await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error managing DND mode: ' + error.message);
            }
        });

        // Contacts command
        this.bot.onText(/\/contacts/, async (msg) => {
            const chatId = msg.chat.id;
            try {
                const contacts = await this.getImportantContacts();
                let message = '👥 *Important Contacts*\n\n';

                if (contacts.length === 0) {
                    message += 'No important contacts configured yet.\n\n';
                } else {
                    contacts.forEach((contact, index) => {
                        message += `${index + 1}. ${contact.name || contact.whatsapp_id}\n`;
                        message += `   📱 ${contact.whatsapp_id}\n`;
                        message += `   📊 ${contact.message_count || 0} messages\n\n`;
                    });
                }

                const keyboard = {
                    inline_keyboard: [
                        [
                            { text: '➕ Add Contact', callback_data: 'add_contact' },
                            { text: '📊 Contact Stats', callback_data: 'contact_stats' }
                        ]
                    ]
                };

                await this.bot.sendMessage(chatId, message, {
                    parse_mode: 'Markdown',
                    reply_markup: keyboard
                });
            } catch (error) {
                await this.bot.sendMessage(chatId, '❌ Error getting contacts: ' + error.message);
            }
        });

        // Help command
        this.bot.onText(/\/help/, async (msg) => {
            const chatId = msg.chat.id;
            const helpMessage = `
🆘 *WhatsApp Bot Help*

*Automatic Features:*
• Auto-responds to common message patterns
• Detects urgent messages and alerts you
• Generates daily summaries at 8 PM
• Manages business hours and DND mode

*Commands:*
/status - Bot and system status
/summary - Generate current summary
/contacts - Manage important contacts
/settings - Configure bot behavior
/dnd - Toggle Do Not Disturb mode

*Message Types Detected:*
• Greetings and casual messages
• File and document requests
• Meeting and scheduling questions
• Availability inquiries
• Urgent messages (keywords: urgent, problem, etc.)

*Notifications:*
You'll receive alerts for messages that need your attention, including urgent messages and requests from important contacts.
            `;
            
            await this.bot.sendMessage(chatId, helpMessage, { parse_mode: 'Markdown' });
        });

        // Handle callback queries (inline keyboard responses)
        this.bot.on('callback_query', async (callbackQuery) => {
            const action = callbackQuery.data;
            const msg = callbackQuery.message;
            
            try {
                await this.handleCallbackQuery(action, msg, callbackQuery);
            } catch (error) {
                console.error('Error handling callback query:', error);
            }
        });

        // Handle errors
        this.bot.on('error', (error) => {
            console.error('Telegram bot error:', error);
        });

        this.bot.on('polling_error', (error) => {
            console.error('Telegram polling error:', error);
        });
    }

    async handleCallbackQuery(action, msg, callbackQuery) {
        const chatId = msg.chat.id;
        const messageId = msg.message_id;

        try {
            switch (action) {
                case 'toggle_dnd':
                    if (this.onToggleDND) {
                        const newStatus = await this.onToggleDND();
                        await this.bot.editMessageText(
                            `🌙 DND mode ${newStatus ? 'enabled' : 'disabled'}.`,
                            { chat_id: chatId, message_id: messageId }
                        );
                    }
                    break;

                case 'dnd_settings':
                    await this.bot.editMessageText(
                        `⚙️ *DND Settings*\n\nUse the configuration file to customize DND settings:\n• Schedule (start/end times)\n• Allow urgent messages\n• Allow important contacts`,
                        { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' }
                    );
                    break;

                case 'add_contact':
                    await this.bot.editMessageText(
                        `➕ *Add Important Contact*\n\nTo add a contact as important:\n1. Wait for a message from them\n2. I'll ask if you want to mark them as important\n3. Or manually edit the config file`,
                        { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' }
                    );
                    break;

                case 'contact_stats':
                    if (this.onContactStats) {
                        const stats = await this.onContactStats();
                        await this.bot.editMessageText(
                            `📊 *Contact Statistics*\n\n${stats}`,
                            { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' }
                        );
                    }
                    break;

                default:
                    if (action.startsWith('respond_')) {
                        const contactId = action.replace('respond_', '');
                        await this.bot.editMessageText(
                            `✅ You chose to respond to this message manually.`,
                            { chat_id: chatId, message_id: messageId }
                        );
                    } else if (action.startsWith('auto_handle_')) {
                        const contactId = action.replace('auto_handle_', '');
                        await this.bot.editMessageText(
                            `🤖 Message will be handled automatically.`,
                            { chat_id: chatId, message_id: messageId }
                        );
                    }
                    break;
            }
        } catch (error) {
            console.error('Error handling callback query:', error);
            await this.bot.editMessageText(
                `❌ Error: ${error.message}`,
                { chat_id: chatId, message_id: messageId }
            );
        }

        // Answer the callback query to remove loading state
        await this.bot.answerCallbackQuery(callbackQuery.id);
    }

    async sendMessage(text, options = {}) {
        if (!this.isInitialized) {
            console.warn('Telegram bot not initialized, cannot send message');
            return;
        }

        try {
            return await this.bot.sendMessage(this.chatId, text, options);
        } catch (error) {
            console.error('Error sending Telegram message:', error);
        }
    }

    async sendAttentionAlert(messageData, reason) {
        const contactName = messageData.contact_name || messageData.from_contact;
        const messagePreview = messageData.body.length > 100 
            ? messageData.body.substring(0, 100) + '...' 
            : messageData.body;

        const alertText = `
⚠️ *Message Needs Attention*

👤 *From:* ${contactName}
📝 *Message:* ${messagePreview}
🔍 *Reason:* ${reason}
⏰ *Time:* ${new Date(messageData.timestamp).toLocaleString()}
        `;

        const keyboard = {
            inline_keyboard: [
                [
                    { text: '✍️ I\'ll Respond', callback_data: `respond_${messageData.from_contact}` },
                    { text: '🤖 Auto Handle', callback_data: `auto_handle_${messageData.from_contact}` }
                ]
            ]
        };

        await this.sendMessage(alertText, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });
    }

    async sendDailySummary(summaryData) {
        const summaryText = `
📝 *Daily WhatsApp Summary*
📅 ${new Date().toLocaleDateString()}

📊 *Statistics:*
• Total messages: ${summaryData.totalMessages}
• Unique contacts: ${summaryData.uniqueContacts}
• Auto responses: ${summaryData.autoResponses}
• Urgent messages: ${summaryData.urgentMessages}

${summaryData.contactsSummary ? `👥 *Active Contacts:*\n${summaryData.contactsSummary}\n` : ''}

${summaryData.requestsSummary ? `📋 *Requests & Follow-ups:*\n${summaryData.requestsSummary}\n` : ''}

${summaryData.urgentSummary ? `🚨 *Urgent Messages:*\n${summaryData.urgentSummary}\n` : ''}

${summaryData.unhandledSummary ? `⏳ *Unhandled Items:*\n${summaryData.unhandledSummary}` : ''}
        `;

        await this.sendMessage(summaryText, { parse_mode: 'Markdown' });
    }

    async sendSystemAlert(message, type = 'info') {
        const emoji = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅'
        };

        const alertText = `${emoji[type] || 'ℹ️'} *System Alert*\n\n${message}`;
        
        await this.sendMessage(alertText, { parse_mode: 'Markdown' });
    }

    async sendStatusUpdate(status) {
        const statusText = `
📊 *Bot Status Update*

🔄 *WhatsApp:* ${status.whatsapp ? '🟢 Connected' : '🔴 Disconnected'}
🔄 *Database:* ${status.database ? '🟢 Online' : '🔴 Offline'}
🔄 *Auto Response:* ${status.autoResponse ? '🟢 Active' : '🟡 Paused'}

📈 *Recent Activity:*
• Messages in last hour: ${status.recentMessages || 0}
• Auto responses sent: ${status.autoResponsesSent || 0}
        `;

        await this.sendMessage(statusText, { parse_mode: 'Markdown' });
    }

    async sendContactUpdate(contactData) {
        const updateText = `
👤 *Contact Update*

*Name:* ${contactData.name}
*Type:* ${contactData.type}
*Status:* ${contactData.status}

${contactData.message ? `*Message:* ${contactData.message}` : ''}
        `;

        await this.sendMessage(updateText, { parse_mode: 'Markdown' });
    }

    // Callback setters for integration with main bot
    setCallbacks(callbacks) {
        this.onSummaryRequest = callbacks.onSummaryRequest;
        this.onToggleDND = callbacks.onToggleDND;
        this.onContactStats = callbacks.onContactStats;
        this.getSystemStatus = callbacks.getSystemStatus;
        this.getDNDStatus = callbacks.getDNDStatus;
        this.getImportantContacts = callbacks.getImportantContacts;
    }

    // Default implementations (can be overridden)
    async getSystemStatus() {
        return {
            whatsapp: true,
            database: true,
            todayMessages: 0,
            autoResponses: 0,
            attentionAlerts: 0,
            uniqueContacts: 0,
            autoResponseEnabled: true,
            businessHoursActive: false,
            dndMode: false
        };
    }

    async getDNDStatus() {
        return false; // Default: DND is off
    }

    async getImportantContacts() {
        return []; // Default: no important contacts
    }

    stop() {
        if (this.bot) {
            this.bot.stopPolling();
            console.log('Telegram bot stopped');
        }
    }
}

module.exports = TelegramBotManager;
