{"level": "error", "message": "Failed to initialize WhatsApp Bot: Protocol error (Runtime.callFunctionOn): Target closed.", "name": "ProtocolError", "originalMessage": "", "service": "whatsapp-bot", "stack": "ProtocolError: Protocol error (Runtime.callFunctionOn): Target closed.\n    at C:\\Users\\<USER>\\Documents\\whasappResponder\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:329:24\n    at new Promise (<anonymous>)\n    at CDPSessionImpl.send (C:\\Users\\<USER>\\Documents\\whasappResponder\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\Connection.js:325:16)\n    at ExecutionContext._ExecutionContext_evaluate (C:\\Users\\<USER>\\Documents\\whasappResponder\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:211:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (C:\\Users\\<USER>\\Documents\\whasappResponder\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\ExecutionContext.js:107:16)", "timestamp": "2025-06-22T15:54:02.176Z"}