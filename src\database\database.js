/**
 * Database Management
 * SQLite database for storing messages, contacts, and bot data
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
    constructor(dbPath = './data/whatsapp_bot.db') {
        this.dbPath = dbPath;
        this.db = null;
    }

    async initialize() {
        return new Promise((resolve, reject) => {
            // Ensure data directory exists
            const dataDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('Connected to SQLite database');
                    this.createTables().then(resolve).catch(reject);
                }
            });
        });
    }

    async createTables() {
        const tables = [
            // Messages table
            `CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                whatsapp_id TEXT UNIQUE,
                from_contact TEXT NOT NULL,
                to_contact TEXT,
                body TEXT,
                timestamp INTEGER NOT NULL,
                type TEXT DEFAULT 'text',
                is_group BOOLEAN DEFAULT 0,
                group_name TEXT,
                needs_attention BOOLEAN DEFAULT 0,
                attention_reason TEXT,
                processed BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Contacts table
            `CREATE TABLE IF NOT EXISTS contacts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                whatsapp_id TEXT UNIQUE NOT NULL,
                name TEXT,
                phone_number TEXT,
                profile_type TEXT DEFAULT 'unknown',
                is_important BOOLEAN DEFAULT 0,
                auto_respond BOOLEAN DEFAULT 0,
                custom_response TEXT,
                last_seen DATETIME,
                message_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Auto responses log
            `CREATE TABLE IF NOT EXISTS auto_responses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                message_id INTEGER,
                response_text TEXT NOT NULL,
                response_reason TEXT,
                sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (message_id) REFERENCES messages (id)
            )`,

            // Daily summaries
            `CREATE TABLE IF NOT EXISTS daily_summaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE UNIQUE NOT NULL,
                total_messages INTEGER DEFAULT 0,
                unique_contacts INTEGER DEFAULT 0,
                urgent_messages INTEGER DEFAULT 0,
                auto_responses INTEGER DEFAULT 0,
                summary_text TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Bot settings
            `CREATE TABLE IF NOT EXISTS bot_settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.run(table);
        }

        // Create indexes for better performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_messages_from_contact ON messages(from_contact)',
            'CREATE INDEX IF NOT EXISTS idx_messages_needs_attention ON messages(needs_attention)',
            'CREATE INDEX IF NOT EXISTS idx_contacts_whatsapp_id ON contacts(whatsapp_id)',
            'CREATE INDEX IF NOT EXISTS idx_contacts_is_important ON contacts(is_important)'
        ];

        for (const index of indexes) {
            await this.run(index);
        }
    }

    // Helper method to promisify database operations
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // Message operations
    async saveMessage(messageData) {
        const sql = `
            INSERT OR REPLACE INTO messages 
            (whatsapp_id, from_contact, to_contact, body, timestamp, type, is_group, group_name, needs_attention, attention_reason)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const params = [
            messageData.whatsapp_id,
            messageData.from_contact,
            messageData.to_contact,
            messageData.body,
            messageData.timestamp,
            messageData.type || 'text',
            messageData.is_group || 0,
            messageData.group_name,
            messageData.needs_attention || 0,
            messageData.attention_reason
        ];

        const result = await this.run(sql, params);
        
        // Update contact info
        await this.updateContact(messageData.from_contact, {
            last_seen: new Date().toISOString(),
            message_count_increment: 1
        });

        return result.id;
    }

    async getRecentMessages(hours = 24, contactId = null) {
        const timestamp = Date.now() - (hours * 60 * 60 * 1000);
        let sql = `
            SELECT m.*, c.name as contact_name, c.profile_type
            FROM messages m
            LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
            WHERE m.timestamp > ?
        `;
        const params = [timestamp];

        if (contactId) {
            sql += ' AND m.from_contact = ?';
            params.push(contactId);
        }

        sql += ' ORDER BY m.timestamp DESC';
        return await this.all(sql, params);
    }

    async getMessagesNeedingAttention() {
        const sql = `
            SELECT m.*, c.name as contact_name, c.profile_type
            FROM messages m
            LEFT JOIN contacts c ON m.from_contact = c.whatsapp_id
            WHERE m.needs_attention = 1 AND m.processed = 0
            ORDER BY m.timestamp DESC
        `;
        return await this.all(sql);
    }

    // Contact operations
    async updateContact(whatsappId, updates) {
        // First, try to get existing contact
        let contact = await this.get('SELECT * FROM contacts WHERE whatsapp_id = ?', [whatsappId]);
        
        if (contact) {
            // Update existing contact
            const updateFields = [];
            const params = [];
            
            Object.keys(updates).forEach(key => {
                if (key === 'message_count_increment') {
                    updateFields.push('message_count = message_count + 1');
                } else {
                    updateFields.push(`${key} = ?`);
                    params.push(updates[key]);
                }
            });
            
            updateFields.push('updated_at = CURRENT_TIMESTAMP');
            params.push(whatsappId);
            
            const sql = `UPDATE contacts SET ${updateFields.join(', ')} WHERE whatsapp_id = ?`;
            await this.run(sql, params);
        } else {
            // Create new contact
            const sql = `
                INSERT INTO contacts (whatsapp_id, name, last_seen, message_count)
                VALUES (?, ?, ?, 1)
            `;
            await this.run(sql, [whatsappId, updates.name || null, updates.last_seen || new Date().toISOString()]);
        }
    }

    async getContact(whatsappId) {
        return await this.get('SELECT * FROM contacts WHERE whatsapp_id = ?', [whatsappId]);
    }

    async getAllContacts() {
        return await this.all('SELECT * FROM contacts ORDER BY message_count DESC');
    }

    async getImportantContacts() {
        return await this.all('SELECT * FROM contacts WHERE is_important = 1');
    }

    // Auto response logging
    async logAutoResponse(messageId, responseText, reason) {
        const sql = `
            INSERT INTO auto_responses (message_id, response_text, response_reason)
            VALUES (?, ?, ?)
        `;
        return await this.run(sql, [messageId, responseText, reason]);
    }

    async getAutoResponseStats(days = 7) {
        const timestamp = Date.now() - (days * 24 * 60 * 60 * 1000);
        const sql = `
            SELECT COUNT(*) as count, response_reason
            FROM auto_responses ar
            JOIN messages m ON ar.message_id = m.id
            WHERE m.timestamp > ?
            GROUP BY response_reason
        `;
        return await this.all(sql, [timestamp]);
    }

    // Daily summary operations
    async saveDailySummary(date, summaryData) {
        const sql = `
            INSERT OR REPLACE INTO daily_summaries 
            (date, total_messages, unique_contacts, urgent_messages, auto_responses, summary_text)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        const params = [
            date,
            summaryData.total_messages,
            summaryData.unique_contacts,
            summaryData.urgent_messages,
            summaryData.auto_responses,
            summaryData.summary_text
        ];

        return await this.run(sql, params);
    }

    async getDailySummary(date) {
        return await this.get('SELECT * FROM daily_summaries WHERE date = ?', [date]);
    }

    // Cleanup operations
    async cleanupOldMessages(daysToKeep = 30) {
        const timestamp = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
        const result = await this.run('DELETE FROM messages WHERE timestamp < ?', [timestamp]);
        console.log(`Cleaned up ${result.changes} old messages`);
        return result.changes;
    }

    async markMessageProcessed(messageId) {
        return await this.run('UPDATE messages SET processed = 1 WHERE id = ?', [messageId]);
    }

    // Settings operations
    async getSetting(key) {
        const result = await this.get('SELECT value FROM bot_settings WHERE key = ?', [key]);
        return result ? result.value : null;
    }

    async setSetting(key, value) {
        const sql = `
            INSERT OR REPLACE INTO bot_settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        `;
        return await this.run(sql, [key, value]);
    }

    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err);
                    } else {
                        console.log('Database connection closed');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = Database;
