/**
 * Response System
 * Handles automatic responses based on patterns and user profiles
 */

class ResponseSystem {
    constructor(database, config, logger) {
        this.database = database;
        this.config = config;
        this.logger = logger;
    }

    async shouldAutoRespond(messageData) {
        try {
            // Check if auto-response is globally disabled
            const autoResponseEnabled = await this.database.getSetting('auto_response_enabled');
            if (autoResponseEnabled === 'false') {
                return { respond: false, reason: 'Auto-response disabled globally' };
            }

            // Check Do Not Disturb mode
            if (this.config.isDoNotDisturbTime()) {
                // Allow responses for urgent messages if configured
                if (this.config.doNotDisturbMode.allowUrgent && messageData.analysis?.hasUrgentKeywords) {
                    return { respond: true, reason: 'urgent_during_dnd' };
                }
                
                // Allow responses for important contacts if configured
                if (this.config.doNotDisturbMode.allowImportantContacts) {
                    const contact = await this.database.getContact(messageData.from_contact);
                    if (contact && contact.is_important) {
                        return { respond: true, reason: 'important_contact_during_dnd' };
                    }
                }
                
                return { respond: true, reason: 'dnd_mode' };
            }

            // Check business hours
            if (!this.config.isBusinessHours()) {
                return { respond: true, reason: 'outside_business_hours' };
            }

            // Check contact-specific settings
            const contact = await this.database.getContact(messageData.from_contact);
            if (contact) {
                if (contact.auto_respond === 0) {
                    return { respond: false, reason: 'Auto-response disabled for this contact' };
                }
                
                if (contact.custom_response) {
                    return { respond: true, reason: 'custom_contact_response' };
                }
            }

            // Check if message matches response patterns
            const patternMatch = this.matchResponsePattern(messageData.body);
            if (patternMatch) {
                return { respond: true, reason: `pattern_match_${patternMatch}` };
            }

            // Check if we've already responded recently to this contact
            const recentResponse = await this.hasRecentAutoResponse(messageData.from_contact, 60); // 60 minutes
            if (recentResponse) {
                return { respond: false, reason: 'Recent auto-response already sent' };
            }

            // Default: don't auto-respond to avoid spam
            return { respond: false, reason: 'No matching criteria' };

        } catch (error) {
            this.logger.error('Error checking if should auto-respond:', error);
            return { respond: false, reason: 'Error in decision process' };
        }
    }

    async generateResponse(messageData, reason) {
        try {
            let response = null;

            switch (reason) {
                case 'dnd_mode':
                    response = this.config.doNotDisturbMode.autoResponse;
                    break;

                case 'outside_business_hours':
                    response = this.config.businessHours.outOfHoursResponse;
                    break;

                case 'custom_contact_response':
                    const contact = await this.database.getContact(messageData.from_contact);
                    response = contact.custom_response;
                    break;

                case 'urgent_during_dnd':
                    response = "I see this is urgent. I'm currently in Do Not Disturb mode but I'll get back to you as soon as possible.";
                    break;

                case 'important_contact_during_dnd':
                    response = "Thanks for your message. Even though it's outside my usual hours, I'll get back to you soon.";
                    break;

                default:
                    if (reason.startsWith('pattern_match_')) {
                        const patternKey = reason.replace('pattern_match_', '');
                        response = this.config.getRandomResponse(patternKey);
                    }
                    break;
            }

            // If no specific response found, use a generic one
            if (!response) {
                response = this.getGenericResponse(messageData);
            }

            // Personalize response if we have contact info
            response = await this.personalizeResponse(response, messageData);

            return response;

        } catch (error) {
            this.logger.error('Error generating response:', error);
            return "Thanks for your message. I'll get back to you soon.";
        }
    }

    matchResponsePattern(messageBody) {
        const body = messageBody.toLowerCase();

        for (const [patternKey, patternData] of Object.entries(this.config.responsePatterns)) {
            if (!patternData.patterns) continue;

            for (const pattern of patternData.patterns) {
                try {
                    // Convert string pattern to RegExp if needed
                    const regex = typeof pattern === 'string' ? new RegExp(pattern, 'i') : pattern;
                    if (regex && typeof regex.test === 'function' && regex.test(body)) {
                        return patternKey;
                    }
                } catch (error) {
                    this.logger.warn(`Invalid regex pattern: ${pattern}`, error);
                    continue;
                }
            }
        }

        return null;
    }

    getGenericResponse(messageData) {
        const genericResponses = [
            "Thanks for your message! I'll get back to you as soon as possible.",
            "Hi! I've received your message and will respond shortly.",
            "Thanks for reaching out. I'll reply to you soon.",
            "Got your message! I'll get back to you as quickly as I can."
        ];

        // Add time-based responses
        const hour = new Date().getHours();
        if (hour < 12) {
            genericResponses.push("Good morning! Thanks for your message. I'll respond soon.");
        } else if (hour < 18) {
            genericResponses.push("Good afternoon! I've received your message and will reply shortly.");
        } else {
            genericResponses.push("Good evening! Thanks for your message. I'll get back to you soon.");
        }

        const randomIndex = Math.floor(Math.random() * genericResponses.length);
        return genericResponses[randomIndex];
    }

    async personalizeResponse(response, messageData) {
        try {
            const contact = await this.database.getContact(messageData.from_contact);
            
            if (contact && contact.name) {
                // Add name if response doesn't already include a greeting
                if (!response.toLowerCase().includes('hi') && 
                    !response.toLowerCase().includes('hello') && 
                    !response.toLowerCase().includes('hey')) {
                    response = `Hi ${contact.name}! ${response}`;
                }
            }

            // Add context based on message analysis
            if (messageData.analysis) {
                if (messageData.analysis.messageType === 'file_request') {
                    response += " I've noted your file request.";
                } else if (messageData.analysis.messageType === 'scheduling') {
                    response += " I'll check my calendar and get back to you.";
                } else if (messageData.analysis.hasUrgentKeywords) {
                    response += " I understand this is urgent and will prioritize my response.";
                }
            }

            return response;

        } catch (error) {
            this.logger.error('Error personalizing response:', error);
            return response; // Return original response if personalization fails
        }
    }

    async hasRecentAutoResponse(contactId, minutesThreshold) {
        try {
            const timestamp = Date.now() - (minutesThreshold * 60 * 1000);
            
            const recentResponse = await this.database.get(`
                SELECT ar.id FROM auto_responses ar
                JOIN messages m ON ar.message_id = m.id
                WHERE m.from_contact = ? AND m.timestamp > ?
                ORDER BY ar.sent_at DESC
                LIMIT 1
            `, [contactId, timestamp]);

            return !!recentResponse;

        } catch (error) {
            this.logger.error('Error checking recent auto-response:', error);
            return false;
        }
    }

    async addCustomResponse(contactId, response) {
        try {
            await this.database.updateContact(contactId, {
                custom_response: response,
                auto_respond: 1
            });
            
            this.logger.info(`Added custom response for contact ${contactId}`);
            return true;

        } catch (error) {
            this.logger.error('Error adding custom response:', error);
            return false;
        }
    }

    async removeCustomResponse(contactId) {
        try {
            await this.database.updateContact(contactId, {
                custom_response: null
            });
            
            this.logger.info(`Removed custom response for contact ${contactId}`);
            return true;

        } catch (error) {
            this.logger.error('Error removing custom response:', error);
            return false;
        }
    }

    async toggleAutoResponse(contactId, enabled) {
        try {
            await this.database.updateContact(contactId, {
                auto_respond: enabled ? 1 : 0
            });
            
            this.logger.info(`${enabled ? 'Enabled' : 'Disabled'} auto-response for contact ${contactId}`);
            return true;

        } catch (error) {
            this.logger.error('Error toggling auto-response:', error);
            return false;
        }
    }

    async getResponseStats(days = 7) {
        try {
            const timestamp = Date.now() - (days * 24 * 60 * 60 * 1000);
            
            const stats = await this.database.all(`
                SELECT 
                    ar.response_reason,
                    COUNT(*) as count,
                    COUNT(DISTINCT m.from_contact) as unique_contacts
                FROM auto_responses ar
                JOIN messages m ON ar.message_id = m.id
                WHERE m.timestamp > ?
                GROUP BY ar.response_reason
                ORDER BY count DESC
            `, [timestamp]);

            return stats;

        } catch (error) {
            this.logger.error('Error getting response stats:', error);
            return [];
        }
    }

    async addResponsePattern(key, patterns, responses) {
        try {
            // Add to config (this would need to be persisted to file)
            this.config.responsePatterns[key] = {
                patterns: patterns.map(p => new RegExp(p, 'i')),
                responses: responses
            };

            // Save to user config file
            this.config.saveUserConfig();
            
            this.logger.info(`Added response pattern: ${key}`);
            return true;

        } catch (error) {
            this.logger.error('Error adding response pattern:', error);
            return false;
        }
    }

    async removeResponsePattern(key) {
        try {
            delete this.config.responsePatterns[key];
            this.config.saveUserConfig();
            
            this.logger.info(`Removed response pattern: ${key}`);
            return true;

        } catch (error) {
            this.logger.error('Error removing response pattern:', error);
            return false;
        }
    }
}

module.exports = ResponseSystem;
