{"userProfiles": {"<EMAIL>": {"name": "Family Group", "type": "family", "autoRespond": false, "customResponse": null, "priority": "low"}}, "responsePatterns": {"greetings": {"patterns": ["^(hi|hello|hey|ciao|salve)", "^good (morning|afternoon|evening)", "^buongiorno|buonasera"], "responses": ["Hi! I'm currently not available. I'll get back to you as soon as possible.", "Hello! Thanks for your message. I'll respond shortly.", "Hey! I'm away right now but will reply soon.", "Ciao! Ti risponderò appena possibile."]}, "fileRequests": {"patterns": ["can you send (me )?the", "please send", "need the (file|document|report)", "where is the", "puoi mandare", "invia(mi)? (il|la|lo)"], "responses": ["I've noted your request for the file. I'll send it to you shortly.", "Thanks for the request. I'll get that file to you as soon as I can.", "Got it! I'll send you the document you requested.", "Ho preso nota della tua richiesta. Ti invierò il file a breve."]}, "meetingQuestions": {"patterns": ["when (is|are) (we|the) meeting", "what time (is )?the meeting", "meeting time", "when do we meet", "quando (è|abbiamo) (la|il) (riunione|meeting)", "che ora (è|abbiamo) (la|il) (riunione|meeting)"], "responses": ["Let me check the calendar and get back to you with the meeting details.", "I'll confirm the meeting time and send you the details shortly.", "Thanks for asking! I'll send you the meeting information in a moment.", "Controllo il calendario e ti faccio sapere i dettagli dell'incontro."]}, "availability": {"patterns": ["are you (free|available)", "can you (talk|call)", "do you have time", "sei (libero|disponibile)", "puoi (parlare|chiamare)", "hai tempo"], "responses": ["I'm not available right now, but I'll get back to you soon to discuss.", "Currently busy, but I'll reach out to you shortly to chat.", "Not available at the moment, but let's connect soon!", "Non sono disponibile ora, ma ti ricontatto presto per parlare."]}, "thanks": {"patterns": ["^(thanks|thank you|grazie|thx)", "appreciate it", "ti ringrazio"], "responses": ["You're welcome!", "No problem!", "Happy to help!", "Prego!", "Di niente!"]}}, "importantContacts": ["<EMAIL>", "<EMAIL>"], "urgencyKeywords": ["urgent", "emergency", "asap", "immediately", "critical", "problem", "issue", "blocked", "help", "stuck", "deadline", "today", "now", "urgente", "emergenza", "subito", "problema", "aiuto", "bloccato", "scadenza", "oggi", "adesso"], "businessHours": {"enabled": true, "timezone": "Europe/Rome", "schedule": {"monday": {"start": "09:00", "end": "18:00"}, "tuesday": {"start": "09:00", "end": "18:00"}, "wednesday": {"start": "09:00", "end": "18:00"}, "thursday": {"start": "09:00", "end": "18:00"}, "friday": {"start": "09:00", "end": "18:00"}, "saturday": null, "sunday": null}, "outOfHoursResponse": "Thanks for your message! I'm currently outside business hours (9 AM - 6 PM, Mon-Fri). I'll get back to you during business hours. / Grazie per il messaggio! Sono fuori dall'orario di lavoro (9-18, Lun-Ven). Ti risponderò durante l'orario lavorativo."}, "doNotDisturbMode": {"enabled": false, "schedule": {"start": "22:00", "end": "08:00"}, "autoResponse": "I'm currently in Do Not Disturb mode. I'll respond to your message tomorrow morning. / Sono in modalità Non Disturbare. Ti risponderò domani mattina.", "allowUrgent": true, "allowImportantContacts": true}, "customSettings": {"maxAutoResponsesPerHour": 3, "summaryTime": "20:00", "cleanupOldMessagesAfterDays": 30, "enableLearning": true, "defaultLanguage": "en", "supportedLanguages": ["en", "it"]}, "ai": {"enabled": true, "features": {"smartResponses": true, "intelligentSummaries": true, "sentimentAnalysis": true, "contextualNotifications": true}, "model": "gpt-3.5-turbo", "maxTokens": 150, "temperature": 0.7, "language": "en", "personality": "professional", "rateLimits": {"requestsPerDay": 1000, "maxCostPerHour": 5.0, "maxCostPerDay": 20.0}, "fallbackToPatterns": true, "privacyMode": true, "modelOptions": {"gpt-4": {"description": "Most capable, highest cost", "costPer1kTokens": {"input": 0.03, "output": 0.06}}, "gpt-4-turbo": {"description": "Fast and capable, moderate cost", "costPer1kTokens": {"input": 0.01, "output": 0.03}}, "gpt-3.5-turbo": {"description": "Good balance of capability and cost", "costPer1kTokens": {"input": 0.0015, "output": 0.002}}, "gpt-4o-mini": {"description": "Fastest and cheapest, basic capability", "costPer1kTokens": {"input": 0.00015, "output": 0.0006}}}}}