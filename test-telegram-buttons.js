#!/usr/bin/env node

/**
 * Test Telegram Buttons
 * Tests if Telegram inline keyboard buttons work correctly
 */

require('dotenv').config();
const TelegramBot = require('node-telegram-bot-api');

const token = process.env.TELEGRAM_BOT_TOKEN;
const chatId = process.env.TELEGRAM_CHAT_ID;

if (!token || !chatId) {
    console.error('❌ TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID must be set in .env');
    process.exit(1);
}

console.log('🧪 Testing Telegram Inline Keyboard Buttons...');
console.log(`Bot Token: ${token.substring(0, 10)}...`);
console.log(`Chat ID: ${chatId}`);

const bot = new TelegramBot(token, { polling: true });

// Test message with inline keyboard
async function sendTestMessage() {
    const keyboard = {
        inline_keyboard: [
            [
                { text: '✅ Test Button 1', callback_data: 'test_button_1' },
                { text: '❌ Test Button 2', callback_data: 'test_button_2' }
            ],
            [
                { text: '🔄 Test Button 3', callback_data: 'test_button_3' }
            ]
        ]
    };

    const message = `
🧪 *Test Inline Keyboard*

Clicca uno dei pulsanti qui sotto per testare se i callback query funzionano correttamente.

Se i pulsanti non funzionano, il problema è nella configurazione del bot Telegram.
    `;

    try {
        await bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: keyboard
        });
        console.log('✅ Test message sent successfully');
    } catch (error) {
        console.error('❌ Error sending test message:', error);
    }
}

// Handle callback queries
bot.on('callback_query', async (callbackQuery) => {
    const action = callbackQuery.data;
    const msg = callbackQuery.message;
    const chatId = msg.chat.id;
    const messageId = msg.message_id;

    console.log(`📱 Callback query received: ${action}`);

    try {
        let responseText = '';
        
        switch (action) {
            case 'test_button_1':
                responseText = '✅ Test Button 1 clicked successfully!';
                break;
            case 'test_button_2':
                responseText = '❌ Test Button 2 clicked successfully!';
                break;
            case 'test_button_3':
                responseText = '🔄 Test Button 3 clicked successfully!';
                break;
            default:
                responseText = `🤔 Unknown button: ${action}`;
        }

        // Edit the message to show the result
        await bot.editMessageText(responseText, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown'
        });

        // Answer the callback query to remove loading state
        await bot.answerCallbackQuery(callbackQuery.id, {
            text: `Button ${action} pressed!`,
            show_alert: false
        });

        console.log(`✅ Callback query handled successfully: ${action}`);

    } catch (error) {
        console.error('❌ Error handling callback query:', error);
        
        // Try to answer the callback query even if there was an error
        try {
            await bot.answerCallbackQuery(callbackQuery.id, {
                text: 'Error handling button press',
                show_alert: true
            });
        } catch (answerError) {
            console.error('❌ Error answering callback query:', answerError);
        }
    }
});

// Handle polling errors
bot.on('polling_error', (error) => {
    console.error('❌ Telegram polling error:', error);
});

// Send test message after a short delay
setTimeout(sendTestMessage, 2000);

console.log('🔄 Bot started. Waiting for button clicks...');
console.log('Press Ctrl+C to stop the test');

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Stopping test bot...');
    bot.stopPolling();
    process.exit(0);
});
