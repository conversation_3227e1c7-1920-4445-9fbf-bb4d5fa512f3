{"name": "whatsapp-intelligent-bot", "version": "1.0.0", "description": "Intelligent WhatsApp bot with automatic responses, message analysis, and Telegram notifications", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "setup": "node setup.js", "test": "node test-bot.js", "test:watch": "nodemon test-bot.js", "backup": "bash scripts/backup.sh", "health": "bash scripts/health-check.sh check", "restart": "bash scripts/health-check.sh restart", "fix": "bash scripts/health-check.sh fix", "get-chat-id": "node get-chat-id.js"}, "keywords": ["whatsapp", "bot", "automation", "telegram", "ai"], "author": "<PERSON><PERSON>", "license": "MIT", "type": "commonjs", "dependencies": {"dotenv": "^16.5.0", "node-cron": "^4.1.1", "node-telegram-bot-api": "^0.66.0", "qrcode-terminal": "^0.12.0", "sqlite3": "^5.1.7", "whatsapp-web.js": "^1.30.0", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}